// 食物库页面
Page({
  data: {
    searchKeyword: '',
    activeCategory: 'all',
    showFoodDetail: false,
    selectedFood: null,
    
    categories: [
      { key: 'all', name: '全部', icon: 'icon-shouye' },
      { key: 'staple', name: '主食', icon: 'icon-tanshui' },
      { key: 'meat', name: '肉类', icon: 'icon-wucan' },
      { key: 'vegetable', name: '蔬菜', icon: 'icon-zitizhifang' },
      { key: 'fruit', name: '水果', icon: 'icon-ewaijiacan' },
      { key: 'dairy', name: '奶制品', icon: 'icon-wucan' },
      { key: 'snack', name: '零食', icon: 'icon-ewaijiacan' }
    ],

    // 模拟食物数据
    foods: [
      {
        id: 1,
        name: '米饭',
        category: 'staple',
        description: '蒸制白米饭，主要提供碳水化合物',
        calories: 116,
        protein: 2.6,
        fat: 0.3,
        carbs: 25.9,
        image: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=60&h=60&fit=crop&crop=center',
        commonWeights: [
          { name: '一小碗', weight: 100 },
          { name: '一中碗', weight: 150 },
          { name: '一大碗', weight: 200 }
        ]
      },
      {
        id: 2,
        name: '鸡胸肉',
        category: 'meat',
        description: '去皮鸡胸肉，高蛋白低脂肪',
        calories: 133,
        protein: 28.9,
        fat: 1.9,
        carbs: 0,
        image: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?w=60&h=60&fit=crop&crop=center',
        commonWeights: [
          { name: '一小块', weight: 80 },
          { name: '一中块', weight: 120 },
          { name: '一大块', weight: 150 }
        ]
      },
      {
        id: 3,
        name: '西兰花',
        category: 'vegetable',
        description: '新鲜西兰花，富含维生素C和膳食纤维',
        calories: 34,
        protein: 4.1,
        fat: 0.4,
        carbs: 4.3,
        image: 'https://images.unsplash.com/photo-1459411621453-7b03977f4bfc?w=60&h=60&fit=crop&crop=center',
        commonWeights: [
          { name: '一小朵', weight: 30 },
          { name: '一中朵', weight: 50 },
          { name: '一大朵', weight: 80 }
        ]
      },
      {
        id: 4,
        name: '苹果',
        category: 'fruit',
        description: '新鲜苹果，富含维生素和膳食纤维',
        calories: 52,
        protein: 0.3,
        fat: 0.2,
        carbs: 13.8,
        image: 'https://images.unsplash.com/photo-1551754655-cd27e38d2076?w=60&h=60&fit=crop&crop=center',
        commonWeights: [
          { name: '一小个', weight: 150 },
          { name: '一中个', weight: 200 },
          { name: '一大个', weight: 250 }
        ]
      },
      {
        id: 5,
        name: '牛奶',
        category: 'dairy',
        description: '全脂牛奶，富含蛋白质和钙质',
        calories: 54,
        protein: 3.0,
        fat: 3.2,
        carbs: 3.4,
        image: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=60&h=60&fit=crop&crop=center',
        commonWeights: [
          { name: '一小杯', weight: 200 },
          { name: '一中杯', weight: 250 },
          { name: '一大杯', weight: 300 }
        ]
      },
      {
        id: 6,
        name: '面包',
        category: 'staple',
        description: '全麦面包片，含有膳食纤维',
        calories: 247,
        protein: 8.4,
        fat: 3.2,
        carbs: 49.0,
        image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=60&h=60&fit=crop&crop=center',
        commonWeights: [
          { name: '一薄片', weight: 25 },
          { name: '一中片', weight: 35 },
          { name: '一厚片', weight: 50 }
        ]
      },
      {
        id: 7,
        name: '鸡蛋',
        category: 'meat',
        description: '鸡蛋，优质蛋白质来源',
        calories: 144,
        protein: 13.3,
        fat: 8.8,
        carbs: 2.8,
        image: 'https://images.unsplash.com/photo-1518569656558-1f25e69d93d7?w=60&h=60&fit=crop&crop=center',
        commonWeights: [
          { name: '一小个', weight: 45 },
          { name: '一中个', weight: 55 },
          { name: '一大个', weight: 65 }
        ]
      },
      {
        id: 8,
        name: '香蕉',
        category: 'fruit',
        description: '新鲜香蕉，富含钾和维生素B6',
        calories: 89,
        protein: 1.1,
        fat: 0.3,
        carbs: 22.8,
        image: 'https://images.unsplash.com/photo-1569288052389-dac9b01ac5b9?w=60&h=60&fit=crop&crop=center',
        commonWeights: [
          { name: '一小根', weight: 100 },
          { name: '一中根', weight: 120 },
          { name: '一大根', weight: 150 }
        ]
      }
    ],

    filteredFoods: []
  },

  onLoad() {
    this.setData({
      filteredFoods: this.data.foods
    });
  },

  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    this.filterFoods();
  },

  onSearch() {
    this.filterFoods();
  },

  onClearSearch() {
    this.setData({
      searchKeyword: ''
    });
    this.filterFoods();
  },

  onSelectCategory(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      activeCategory: category
    });
    this.filterFoods();
  },

  filterFoods() {
    const { foods, searchKeyword, activeCategory } = this.data;
    let filtered = foods;

    // 按分类过滤
    if (activeCategory !== 'all') {
      filtered = filtered.filter(food => food.category === activeCategory);
    }

    // 按关键词过滤
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.trim().toLowerCase();
      filtered = filtered.filter(food => 
        food.name.toLowerCase().includes(keyword) ||
        food.description.toLowerCase().includes(keyword)
      );
    }

    this.setData({
      filteredFoods: filtered
    });
  },

  onSelectFood(e) {
    const food = e.currentTarget.dataset.food;
    this.setData({
      selectedFood: food,
      showFoodDetail: true
    });
  },

  onCloseDetail() {
    this.setData({
      showFoodDetail: false,
      selectedFood: null
    });
  },

  stopPropagation() {
    // 阻止事件冒泡
  },

  onAddToMeal() {
    const { selectedFood } = this.data;
    
    wx.showActionSheet({
      itemList: ['添加到早餐', '添加到午餐', '添加到晚餐', '添加到加餐'],
      success: (res) => {
        const meals = ['breakfast', 'lunch', 'dinner', 'snack'];
        const mealNames = ['早餐', '午餐', '晚餐', '加餐'];
        const selectedMeal = meals[res.tapIndex];
        
        // 这里可以添加到对应的餐次
        wx.showToast({
          title: `已添加到${mealNames[res.tapIndex]}`,
          icon: 'success'
        });
        
        this.onCloseDetail();
      }
    });
  }
});
