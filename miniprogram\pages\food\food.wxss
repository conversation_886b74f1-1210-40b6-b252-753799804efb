/* 饮食记录页面样式 */
.page-container {
  background-color: #f8fafc;
  min-height: 100vh;
}

.container {
  padding-bottom: 120rpx;
}

/* 顶部导航 */
.top-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: white;
  border-bottom: 2rpx solid #f1f5f9;
}

.nav-left, .nav-right {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  font-size: 32rpx;
  color: var(--text-secondary);
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
}

/* 日期选择 */
.date-selector {
  padding: 32rpx;
  background: white;
}

.date-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 64rpx;
}

.date-arrow {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 50%;
  border: 2rpx solid #e2e8f0;
}

.arrow-icon {
  font-size: 32rpx;
  color: var(--text-secondary);
  font-weight: bold;
}

.date-info {
  text-align: center;
}

.date-main {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.date-sub {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 热量总览卡片 */
.calorie-overview-card {
  background: linear-gradient(135deg, var(--primary-color), #3b82f6);
  border-radius: 32rpx;
  padding: 40rpx;
  margin: 32rpx;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(37, 99, 235, 0.3);
}

/* 水壶容器 */
.water-container {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.water-bottle {
  width: 120rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx 20rpx 40rpx 40rpx;
  position: relative;
  margin-right: 40rpx;
  overflow: hidden;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

/* 水流动画 */
.water-flow {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 50%,
    rgba(255, 255, 255, 0.9) 100%);
  border-radius: 0 0 36rpx 36rpx;
  transition: height 1s ease-in-out;
  overflow: hidden;
}

/* 水波动画 */
.water-wave {
  position: absolute;
  top: -20rpx;
  left: -50%;
  width: 200%;
  height: 40rpx;
  background: radial-gradient(ellipse at center,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%);
  border-radius: 50%;
  animation: wave 3s ease-in-out infinite;
}

.wave1 {
  animation-delay: 0s;
}

.wave2 {
  animation-delay: 1s;
  opacity: 0.7;
}

.wave3 {
  animation-delay: 2s;
  opacity: 0.5;
}

@keyframes wave {
  0%, 100% {
    transform: translateX(-25%) rotate(0deg);
  }
  50% {
    transform: translateX(25%) rotate(180deg);
  }
}

/* 刻度线 */
.scale-lines {
  position: absolute;
  left: -30rpx;
  top: 0;
  bottom: 0;
  width: 30rpx;
}

.scale-line {
  position: absolute;
  right: 0;
  width: 20rpx;
  height: 2rpx;
  background: rgba(255, 255, 255, 0.5);
}

.scale-text {
  position: absolute;
  right: 25rpx;
  top: -10rpx;
  font-size: 16rpx;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
}

/* 热量数据 */
.calorie-data {
  flex: 1;
}

.calorie-main {
  text-align: left;
}

.calorie-value {
  font-size: 72rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.calorie-label {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 8rpx;
}

.calorie-progress {
  font-size: 24rpx;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.calorie-stats {
  display: flex;
  justify-content: space-between;
  text-align: center;
}

.stat-item {
  flex: 1;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 20rpx;
  opacity: 0.9;
}

/* 智能提示卡片 */
.smart-tip-card {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 0 32rpx 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.tip-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.tip-icon-text {
  font-size: 32rpx;
  color: white;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.tip-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 餐次容器 */
.meals-container {
  padding: 0 32rpx;
}

.meal-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.meal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.meal-info {
  display: flex;
  align-items: center;
}

.meal-icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.meal-icon-container.breakfast {
  background: #fef3c7;
}

.meal-icon-container.lunch {
  background: #fed7aa;
}

.meal-icon-container.dinner {
  background: #e9d5ff;
}

.meal-icon-container.snack {
  background: #dcfce7;
}

.meal-icon {
  font-size: 32rpx;
}

.meal-icon-container.breakfast .meal-icon {
  color: #f59e0b;
}

.meal-icon-container.lunch .meal-icon {
  color: #ea580c;
}

.meal-icon-container.dinner .meal-icon {
  color: #8b5cf6;
}

.meal-icon-container.snack .meal-icon {
  color: #22c55e;
}

.meal-details {
  flex: 1;
}

.meal-name {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 4rpx;
}

.meal-calories {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.meal-add-btn {
  background: var(--primary-color);
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
}

/* 食物列表 */
.food-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.food-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  background: #f8fafc;
  border-radius: 16rpx;
}

.food-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.food-image {
  width: 64rpx;
  height: 64rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  background: #e2e8f0;
}

.food-details {
  flex: 1;
}

.food-name {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4rpx;
}

.food-amount {
  font-size: 20rpx;
  color: var(--text-secondary);
}

.food-calories {
  font-size: 24rpx;
  font-weight: 600;
  color: var(--primary-color);
}

/* 空状态 */
.meal-empty {
  text-align: center;
  padding: 40rpx 0;
  color: var(--text-tertiary);
}

.empty-text {
  font-size: 24rpx;
}

/* 悬浮按钮 */
.fab-container {
  position: fixed;
  bottom: 120rpx;
  right: 48rpx;
  z-index: 100;
}

.fab-button {
  width: 112rpx;
  height: 112rpx;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(37, 99, 235, 0.4);
  animation: float 3s ease-in-out infinite;
}

.fab-icon {
  font-size: 40rpx;
  color: white;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8rpx); }
}

/* 添加选项弹窗 */
.add-options-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
}

.add-options-container {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  padding: 48rpx 32rpx;
  width: 100%;
  max-width: 750rpx;
}

.add-option {
  display: flex;
  align-items: center;
  padding: 32rpx;
  margin-bottom: 16rpx;
  background: #f8fafc;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.add-option:active {
  transform: scale(0.98);
  background: #e2e8f0;
}

.option-icon {
  font-size: 40rpx;
  color: var(--primary-color);
  margin-right: 32rpx;
}

.option-text {
  font-size: 32rpx;
  color: var(--text-primary);
  font-weight: 500;
}