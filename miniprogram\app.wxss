/* 智食派小程序全局样式 */
@import "iconfont.wxss";
@import "iconfont2.wxss";

/* 全局变量 */
page {
  --primary-color: #2563eb;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --bg-primary: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --shadow: 0 4px 20px rgba(0,0,0,0.08);
}

/* 基础样式重置 */
view, text, image, input, button {
  box-sizing: border-box;
}

page {
  background-color: var(--bg-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--text-primary);
  line-height: 1.5;
}

/* 通用容器 */
.container {
  padding: 32rpx;
}

.page-container {
  min-height: 100vh;
  background-color: var(--bg-primary);
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx 32rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: var(--shadow);
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
}

.card-title .icon {
  margin-right: 16rpx;
  color: var(--primary-color);
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.gradient-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, #3b82f6 100%);
  color: white;
}

/* 按钮样式 */
.btn {
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid var(--border-color);
  color: var(--text-primary);
}

.btn-round {
  border-radius: 50%;
  width: 112rpx;
  height: 112rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 进度条样式 */
.progress-bar {
  width: 100%;
  height: 16rpx;
  background-color: #e2e8f0;
  border-radius: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.progress-success {
  background: linear-gradient(90deg, var(--success-color), var(--primary-color));
}

.progress-warning {
  background: linear-gradient(90deg, var(--warning-color), var(--danger-color));
}

/* 文本样式 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-center {
  text-align: center;
}

.text-bold {
  font-weight: bold;
}

.text-large {
  font-size: 48rpx;
}

.text-small {
  font-size: 24rpx;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 间距样式 */
.mt-16 { margin-top: 16rpx; }
.mt-32 { margin-top: 32rpx; }
.mb-16 { margin-bottom: 16rpx; }
.mb-32 { margin-bottom: 32rpx; }
.ml-16 { margin-left: 16rpx; }
.mr-16 { margin-right: 16rpx; }

.p-16 { padding: 16rpx; }
.p-32 { padding: 32rpx; }
.px-16 { padding-left: 16rpx; padding-right: 16rpx; }
.py-16 { padding-top: 16rpx; padding-bottom: 16rpx; }

/* 圆角样式 */
.rounded {
  border-radius: 16rpx;
}

.rounded-lg {
  border-radius: 24rpx;
}

.rounded-xl {
  border-radius: 32rpx;
}

.rounded-full {
  border-radius: 50%;
}

/* 阴影样式 */
.shadow {
  box-shadow: var(--shadow);
}

/* 动画样式 */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20rpx); }
}

/* 响应式网格 */
.grid {
  display: grid;
  gap: 24rpx;
}

.grid-2 {
  grid-template-columns: 1fr 1fr;
}

.grid-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

/* 食物条目样式 */
.food-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}

.food-image {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
}

.food-info {
  flex: 1;
}

.food-name {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.food-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-top: 8rpx;
}

.food-calories {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
}
