// 智食派首页 - 智能仪表盘
const app = getApp();
const { userAPI, foodAPI, sportAPI, showLoading, hideLoading, showError } = require('../../utils/api');
const { calculateBMI, getBMIStatus, calculateBMR, calculateTDEE, calculateWeightLossCalories, getCalorieStatusColor, getTodayDate } = require('../../utils/calculator');
const { MEAL_NAMES, MEAL_ICONS, COLORS } = require('../../utils/constants');

Page({
  data: {
    // 用户档案数据
    userProfile: null,
    bmi: 0,
    bmiStatus: {},
    bmr: 0,
    tdee: 0,
    targetCalories: 0,

    // 今日数据
    todayDate: '',
    todayNutrition: {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0
    },
    todaySportCalories: 0,
    remainingCalories: 0,
    calorieProgress: 0,
    statusColor: COLORS.SUCCESS,

    // 营养素分布
    nutrientRatio: {
      protein: 0,
      carbs: 0,
      fat: 0
    },

    // 目标完成度
    goalProgress: 0,

    // 激励文字
    motivationText: '',
    recordDays: 0,

    // 弹窗状态
    showAddOptions: false,
    currentMealType: '',

    // 餐次状态
    mealStatus: {
      breakfast: false,
      lunch: false,
      dinner: false,
      snack: false
    },
    sportStatus: false,

    // 智能体重管理
    weightStatus: {
      mode: 'normal', // normal, remind, abnormal
      currentWeight: 59.8,
      lastWeight: 60.1,
      changeText: '-0.3kg',
      changeIcon: '📈',
      daysSince: 3,
      statusText: '减重进展良好',
      nextReminder: '周五提醒'
    },

    // 体重设置
    weightSettings: {
      reminderFrequency: 'twice_weekly',
      normalRange: 0.5,
      reminderTime: '07:00',
      displayMode: 'trend'
    },

    // 体重设置选项
    reminderFrequencyOptions: [
      { value: 'daily', label: '每天' },
      { value: 'twice_weekly', label: '每周2-3次' },
      { value: 'weekly', label: '每周1次' },
      { value: 'custom', label: '自定义' }
    ],
    normalRangeOptions: [
      { value: 0.3, label: '±0.3kg(敏感)' },
      { value: 0.5, label: '±0.5kg(标准)' },
      { value: 1.0, label: '±1kg(宽松)' }
    ],
    displayModeOptions: [
      { value: 'trend', label: '显示趋势' },
      { value: 'value_only', label: '仅显示数值' }
    ],

    // 弹窗状态
    showWeightSettings: false,
    showWeightInput: false,
    weightInputTitle: '',
    weightInputSubtitle: '',
    weightInputPlaceholder: '',
    weightInputFocus: false,
    inputWeight: '',

    // 加载状态
    loading: true,
    refreshing: false
  },

  onLoad() {
    this.setData({
      todayDate: getTodayDate()
    });
    this.initData();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  onReady() {
    // 页面渲染完成后绘制圆环
    setTimeout(() => {
      this.drawProgressRing(this.data.calorieProgress);
    }, 100);
  },

  onPullDownRefresh() {
    this.setData({ refreshing: true });
    this.refreshData().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 初始化数据
  async initData() {
    try {
      showLoading('加载中...');
      await this.loadUserProfile();
      await this.loadTodayData();
      this.calculateDashboardData();
      this.generateMotivationText();
      this.updateWeightStatus();
    } catch (error) {
      console.error('初始化数据失败:', error);
      showError('数据加载失败');
    } finally {
      hideLoading();
      this.setData({ loading: false });
    }
  },

  // 刷新数据
  async refreshData() {
    try {
      await this.loadTodayData();
      this.calculateDashboardData();
    } catch (error) {
      console.error('刷新数据失败:', error);
      showError('刷新失败');
    }
  },

  // 加载用户档案
  async loadUserProfile() {
    try {
      const profile = await userAPI.getUserProfile();
      this.setData({ userProfile: profile });

      // 计算基础数据
      const bmi = calculateBMI(profile.weight, profile.height);
      const bmiStatus = getBMIStatus(bmi);
      const bmr = calculateBMR(profile.weight, profile.height, profile.age, profile.gender);
      const tdee = calculateTDEE(bmr, profile.activityLevel);
      const targetCalories = calculateWeightLossCalories(tdee, profile.goals?.weightLossRate || 0.5);

      this.setData({
        bmi,
        bmiStatus,
        bmr,
        tdee,
        targetCalories
      });
    } catch (error) {
      console.error('加载用户档案失败:', error);
      // 如果没有用户档案，使用默认数据进行演示
      const defaultProfile = {
        height: 165,
        weight: 60,
        targetWeight: 55,
        age: 25,
        gender: 'female',
        activityLevel: 'light'
      };

      this.setData({ userProfile: defaultProfile });

      const bmi = calculateBMI(defaultProfile.weight, defaultProfile.height);
      const bmiStatus = getBMIStatus(bmi);
      const bmr = calculateBMR(defaultProfile.weight, defaultProfile.height, defaultProfile.age, defaultProfile.gender);
      const tdee = calculateTDEE(bmr, defaultProfile.activityLevel);
      const targetCalories = calculateWeightLossCalories(tdee, 0.5);

      this.setData({
        bmi,
        bmiStatus,
        bmr,
        tdee,
        targetCalories
      });
    }
  },

  // 加载今日数据
  async loadTodayData() {
    const today = this.data.todayDate;

    try {
      // 并行加载饮食和运动数据
      const [foodRecords, sportRecords] = await Promise.all([
        foodAPI.getFoodRecords(today).catch(() => []),
        sportAPI.getSportRecords(today).catch(() => [])
      ]);

      // 计算今日营养摄入
      let totalCalories = 0;
      let totalProtein = 0;
      let totalCarbs = 0;
      let totalFat = 0;

      foodRecords.forEach(record => {
        if (record.foods && Array.isArray(record.foods)) {
          record.foods.forEach(food => {
            totalCalories += food.calories || 0;
            totalProtein += food.protein || 0;
            totalCarbs += food.carbs || 0;
            totalFat += food.fat || 0;
          });
        }
      });

      // 计算今日运动消耗
      let totalSportCalories = 0;
      sportRecords.forEach(record => {
        totalSportCalories += record.calories || 0;
      });

      // 计算餐次状态
      const mealStatus = {
        breakfast: false,
        lunch: false,
        dinner: false,
        snack: false
      };

      foodRecords.forEach(record => {
        if (record.mealType && record.foods && record.foods.length > 0) {
          mealStatus[record.mealType] = true;
        }
      });

      this.setData({
        todayNutrition: {
          calories: totalCalories,
          protein: totalProtein,
          carbs: totalCarbs,
          fat: totalFat
        },
        todaySportCalories: totalSportCalories,
        mealStatus: mealStatus,
        sportStatus: totalSportCalories > 0
      });
    } catch (error) {
      console.error('加载今日数据失败:', error);
      // 使用模拟数据进行演示
      this.setData({
        todayNutrition: {
          calories: 1200,
          protein: 60,
          carbs: 150,
          fat: 40
        },
        todaySportCalories: 300
      });
    }
  },

  // 计算仪表盘数据
  calculateDashboardData() {
    const { todayNutrition, todaySportCalories, targetCalories, userProfile } = this.data;

    // 计算剩余热量（考虑运动消耗）
    const netCalories = todayNutrition.calories - todaySportCalories;
    const remainingCalories = targetCalories - netCalories;
    const calorieProgress = Math.min((netCalories / targetCalories) * 100, 100);

    // 获取状态颜色
    const statusColor = getCalorieStatusColor(netCalories, targetCalories);

    // 计算营养素占比
    const totalNutrientCalories =
      (todayNutrition.protein * 4) +
      (todayNutrition.carbs * 4) +
      (todayNutrition.fat * 9);

    let nutrientRatio = { protein: 0, carbs: 0, fat: 0 };
    if (totalNutrientCalories > 0) {
      nutrientRatio = {
        protein: Math.round(((todayNutrition.protein * 4) / totalNutrientCalories) * 100),
        carbs: Math.round(((todayNutrition.carbs * 4) / totalNutrientCalories) * 100),
        fat: Math.round(((todayNutrition.fat * 9) / totalNutrientCalories) * 100)
      };
    }

    // 计算目标完成度
    let goalProgress = 0;
    if (userProfile && userProfile.targetWeight) {
      const totalWeightLoss = userProfile.weight - userProfile.targetWeight;
      const currentProgress = userProfile.weight - userProfile.weight; // 这里需要获取最新体重
      goalProgress = totalWeightLoss > 0 ? (currentProgress / totalWeightLoss) * 100 : 0;
    }

    this.setData({
      remainingCalories,
      calorieProgress,
      statusColor,
      nutrientRatio,
      goalProgress
    });

    // 绘制圆环进度条
    this.drawProgressRing(calorieProgress);
  },

  // 绘制圆环进度条
  drawProgressRing(progress) {
    const ctx = wx.createCanvasContext('progressCanvas', this);
    // Canvas实际尺寸是320rpx，对应160px
    const centerX = 80; // 160px / 2
    const centerY = 80;
    const radius = 65; // 稍大的半径，给文字更多空间
    const lineWidth = 8; // 更细的线条

    // 清除画布
    ctx.clearRect(0, 0, 160, 160);

    // 绘制背景圆环
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.setStrokeStyle('#e2e8f0'); // 与UI配色一致的浅灰色
    ctx.setLineWidth(lineWidth);
    ctx.stroke();

    // 绘制进度圆环（逆时针）
    if (progress > 0) {
      ctx.beginPath();
      // 从12点位置开始，逆时针绘制
      const startAngle = -Math.PI / 2; // 12点位置
      const endAngle = startAngle - (progress / 100) * 2 * Math.PI; // 逆时针
      ctx.arc(centerX, centerY, radius, startAngle, endAngle, true); // true表示逆时针
      ctx.setStrokeStyle('#2563eb'); // 使用主色调蓝色，与UI配色一致
      ctx.setLineWidth(lineWidth);
      ctx.setLineCap('round');
      ctx.stroke();
    }

    ctx.draw();
  },

  // 生成激励文字
  generateMotivationText() {
    // 模拟用户坚持天数，实际应该从用户数据中获取
    const recordDays = this.calculateRecordDays();

    const milestones = [
      { days: 1, text: "🎉 恭喜开启健康之旅！" },
      { days: 3, text: "💪 坚持3天，习惯正在养成" },
      { days: 7, text: "⭐ 一周坚持，你很棒！" },
      { days: 14, text: "🔥 两周不间断，意志力超强" },
      { days: 21, text: "🏆 21天习惯养成，你做到了！" },
      { days: 30, text: "👑 一个月坚持，成就解锁！" },
      { days: 60, text: "🌟 两个月坚持，蜕变在继续" },
      { days: 100, text: "💎 百日坚持，你是真正的勇士！" }
    ];

    // 找到对应的里程碑
    const milestone = milestones.reverse().find(m => recordDays >= m.days);

    let motivationText;
    if (milestone) {
      motivationText = `${milestone.text} 已坚持${recordDays}天`;
    } else {
      motivationText = `🔥 您已坚持记录 ${recordDays} 天，继续加油！`;
    }

    this.setData({
      motivationText,
      recordDays
    });
  },

  // 计算用户坚持记录天数
  calculateRecordDays() {
    // 这里应该根据实际的用户记录数据计算
    // 暂时返回模拟数据
    return 15;
  },

  // 快捷操作 - 拍照识别
  onTakePhoto() {
    wx.navigateTo({
      url: '/pages/camera/camera'
    });
  },

  // 快捷操作 - 记录体重
  onRecordWeight() {
    wx.showModal({
      title: '记录体重',
      editable: true,
      placeholderText: '请输入体重(kg)',
      success: async (res) => {
        if (res.confirm && res.content) {
          const weight = parseFloat(res.content);
          if (weight > 0 && weight < 300) {
            try {
              showLoading('记录中...');
              await userAPI.addWeightRecord(weight);
              // 更新用户档案中的体重
              const updatedProfile = { ...this.data.userProfile, weight };
              this.setData({ userProfile: updatedProfile });
              this.calculateDashboardData();
              wx.showToast({
                title: '记录成功',
                icon: 'success'
              });
            } catch (error) {
              showError('记录失败');
            } finally {
              hideLoading();
            }
          } else {
            showError('请输入有效的体重');
          }
        }
      }
    });
  },

  // 快捷操作 - 记录饮食
  onRecordFood() {
    wx.navigateTo({
      url: '/pages/food/food'
    });
  },

  // 快捷操作 - 记录运动
  onRecordSport() {
    wx.navigateTo({
      url: '/pages/sport/sport'
    });
  },

  // 查看详细报告
  onViewReport() {
    wx.switchTab({
      url: '/pages/report/report'
    });
  },

  // 查看个人中心
  onViewProfile() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    });
  },

  // 添加餐次
  onAddMeal(e) {
    const mealType = e.currentTarget.dataset.meal;
    this.setData({
      currentMealType: mealType,
      showAddOptions: true
    });
  },

  // 添加运动
  onAddSport() {
    this.onRecordSport();
  },

  // 显示添加选项弹窗
  onShowAddOptions() {
    this.setData({
      showAddOptions: true
    });
  },

  // 隐藏添加选项弹窗
  onHideAddOptions() {
    this.setData({
      showAddOptions: false,
      currentMealType: ''
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击弹窗内容时关闭弹窗
  },

  // 拍照识别
  onTakePhoto() {
    this.onHideAddOptions();
    const mealType = this.data.currentMealType;
    const url = mealType ? `/pages/camera/camera?meal=${mealType}` : '/pages/camera/camera';
    wx.navigateTo({
      url: url
    });
  },

  // 语音记录
  onVoiceRecord() {
    this.onHideAddOptions();
    wx.showToast({
      title: '语音功能开发中',
      icon: 'none'
    });
  },

  // 打开食物库
  onOpenFoodDatabase() {
    this.onHideAddOptions();
    const mealType = this.data.currentMealType;
    const url = mealType ? `/pages/food-database/food-database?meal=${mealType}` : '/pages/food-database/food-database';
    wx.navigateTo({
      url: url
    });
  },

  // ==================== 智能体重管理 ====================

  // 更新体重状态
  updateWeightStatus() {
    // 模拟获取最新体重记录
    const weightRecords = this.getWeightRecords();
    const reminderStatus = this.getWeightReminderStatus(weightRecords);

    if (reminderStatus.shouldRemind) {
      // 提醒状态
      this.setData({
        weightStatus: {
          mode: 'remind',
          lastWeight: weightRecords.length > 0 ? weightRecords[0].weight : 60.0,
          daysSince: reminderStatus.daysSince
        }
      });
    } else if (weightRecords.length >= 2) {
      // 有历史记录，分析变化
      const current = weightRecords[0];
      const previous = weightRecords[1];
      const analysis = this.analyzeWeightChange(current.weight, previous.weight);

      this.setData({
        weightStatus: {
          mode: analysis.status === 'abnormal' ? 'abnormal' : 'normal',
          currentWeight: current.weight,
          lastWeight: previous.weight,
          changeText: analysis.changeText,
          changeIcon: analysis.trend === 'down' ? '📈' : analysis.trend === 'up' ? '📊' : '📊',
          daysSince: this.calculateDaysBetween(current.record_date, previous.record_date),
          statusText: this.getWeightStatusText(analysis),
          nextReminder: this.getNextReminderText()
        }
      });
    } else {
      // 首次使用或数据不足
      this.setData({
        weightStatus: {
          mode: 'remind',
          lastWeight: 0,
          daysSince: 0
        }
      });
    }
  },

  // 获取体重记录（模拟数据）
  getWeightRecords() {
    // 实际应该从数据库获取
    return [
      { weight: 59.8, record_date: '2024-01-15', record_time: '07:30' },
      { weight: 60.1, record_date: '2024-01-12', record_time: '07:15' },
      { weight: 60.5, record_date: '2024-01-09', record_time: '07:20' }
    ];
  },

  // 获取体重提醒状态
  getWeightReminderStatus(records) {
    if (records.length === 0) {
      return { shouldRemind: true, daysSince: 0 };
    }

    const now = new Date();
    const lastRecord = new Date(records[0].record_date);
    const daysSince = Math.floor((now - lastRecord) / (1000 * 60 * 60 * 24));

    const { reminderFrequency } = this.data.weightSettings;

    const shouldRemind = {
      'daily': daysSince >= 1,
      'twice_weekly': daysSince >= 3,
      'weekly': daysSince >= 7,
      'custom': daysSince >= 3 // 简化处理
    };

    return {
      shouldRemind: shouldRemind[reminderFrequency],
      daysSince: daysSince
    };
  },

  // 分析体重变化
  analyzeWeightChange(currentWeight, previousWeight) {
    const change = currentWeight - previousWeight;
    const absChange = Math.abs(change);
    const { normalRange } = this.data.weightSettings;

    return {
      change: change,
      changeText: change > 0 ? `+${change.toFixed(1)}kg` : `${change.toFixed(1)}kg`,
      status: absChange > normalRange ? 'abnormal' : 'normal',
      trend: change > 0.1 ? 'up' : change < -0.1 ? 'down' : 'stable'
    };
  },

  // 获取体重状态文字
  getWeightStatusText(analysis) {
    if (analysis.status === 'abnormal') {
      return '波动较大，请关注';
    } else if (analysis.trend === 'down') {
      return '减重进展良好';
    } else if (analysis.trend === 'up') {
      return '体重有所上升';
    } else {
      return '保持良好';
    }
  },

  // 获取下次提醒文字
  getNextReminderText() {
    const { reminderFrequency } = this.data.weightSettings;
    const textMap = {
      'daily': '明天提醒',
      'twice_weekly': '周五提醒',
      'weekly': '下周提醒',
      'custom': '按设置提醒'
    };
    return textMap[reminderFrequency] || '按设置提醒';
  },

  // 计算日期间隔
  calculateDaysBetween(date1, date2) {
    const d1 = new Date(date1);
    const d2 = new Date(date2);
    return Math.floor((d1 - d2) / (1000 * 60 * 60 * 24));
  },

  // 智能体重记录点击
  onSmartWeightRecord() {
    const { weightStatus } = this.data;

    let title = '记录体重';
    let subtitle = '';
    let placeholder = '请输入体重';

    if (weightStatus.mode === 'remind') {
      title = '该测量体重了！';
      subtitle = weightStatus.daysSince > 0 ? `距离上次记录已${weightStatus.daysSince}天` : '';
    }

    // 智能预填充
    if (weightStatus.currentWeight) {
      placeholder = `${weightStatus.currentWeight}`;
    } else if (weightStatus.lastWeight) {
      placeholder = `${weightStatus.lastWeight}`;
    }

    this.setData({
      showWeightInput: true,
      weightInputTitle: title,
      weightInputSubtitle: subtitle,
      weightInputPlaceholder: placeholder,
      weightInputFocus: true,
      inputWeight: ''
    });
  },

  // 体重设置
  onWeightSettings() {
    this.setData({
      showWeightSettings: true
    });
  },

  // 隐藏体重设置
  onHideWeightSettings() {
    this.setData({
      showWeightSettings: false
    });
  },

  // 隐藏体重输入
  onHideWeightInput() {
    this.setData({
      showWeightInput: false,
      weightInputFocus: false,
      inputWeight: ''
    });
  },

  // 体重输入
  onWeightInput(e) {
    this.setData({
      inputWeight: e.detail.value
    });
  },

  // 确认体重记录
  async onConfirmWeight() {
    const weight = parseFloat(this.data.inputWeight);

    if (!weight || weight < 30 || weight > 200) {
      wx.showToast({
        title: '请输入有效体重(30-200kg)',
        icon: 'none'
      });
      return;
    }

    try {
      showLoading('记录中...');

      // 这里应该调用API保存体重记录
      // await userAPI.addWeightRecord(weight);

      // 更新本地状态
      this.updateWeightAfterRecord(weight);

      this.onHideWeightInput();

      wx.showToast({
        title: '记录成功',
        icon: 'success'
      });

    } catch (error) {
      showError('记录失败');
    } finally {
      hideLoading();
    }
  },

  // 记录后更新体重状态
  updateWeightAfterRecord(newWeight) {
    const { weightStatus } = this.data;
    const previousWeight = weightStatus.currentWeight || weightStatus.lastWeight || newWeight;
    const analysis = this.analyzeWeightChange(newWeight, previousWeight);

    this.setData({
      weightStatus: {
        mode: analysis.status === 'abnormal' ? 'abnormal' : 'normal',
        currentWeight: newWeight,
        lastWeight: previousWeight,
        changeText: analysis.changeText,
        changeIcon: analysis.trend === 'down' ? '📈' : '📊',
        daysSince: 0,
        statusText: this.getWeightStatusText(analysis),
        nextReminder: this.getNextReminderText()
      }
    });
  },

  // 选择提醒频率
  onSelectReminderFrequency(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'weightSettings.reminderFrequency': value
    });
  },

  // 选择正常范围
  onSelectNormalRange(e) {
    const value = parseFloat(e.currentTarget.dataset.value);
    this.setData({
      'weightSettings.normalRange': value
    });
  },

  // 选择显示模式
  onSelectDisplayMode(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'weightSettings.displayMode': value
    });
  },

  // 提醒时间改变
  onReminderTimeChange(e) {
    this.setData({
      'weightSettings.reminderTime': e.detail.value
    });
  },

  // 保存体重设置
  async onSaveWeightSettings() {
    try {
      showLoading('保存中...');

      // 这里应该调用API保存设置
      // await userAPI.saveWeightSettings(this.data.weightSettings);

      this.onHideWeightSettings();
      this.updateWeightStatus(); // 重新计算状态

      wx.showToast({
        title: '设置已保存',
        icon: 'success'
      });

    } catch (error) {
      showError('保存失败');
    } finally {
      hideLoading();
    }
  }

});
