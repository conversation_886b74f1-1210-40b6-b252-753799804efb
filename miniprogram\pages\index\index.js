// 智食派首页 - 智能仪表盘
const app = getApp();
const { userAPI, foodAPI, sportAPI, showLoading, hideLoading, showError } = require('../../utils/api');
const { calculateBMI, getBMIStatus, calculateBMR, calculateTDEE, calculateWeightLossCalories, getCalorieStatusColor, getTodayDate } = require('../../utils/calculator');
const { MEAL_NAMES, MEAL_ICONS, COLORS } = require('../../utils/constants');

Page({
  data: {
    // 用户档案数据
    userProfile: null,
    bmi: 0,
    bmiStatus: {},
    bmr: 0,
    tdee: 0,
    targetCalories: 0,

    // 今日数据
    todayDate: '',
    todayNutrition: {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0
    },
    todaySportCalories: 0,
    remainingCalories: 0,
    calorieProgress: 0,
    statusColor: COLORS.SUCCESS,

    // 营养素分布
    nutrientRatio: {
      protein: 0,
      carbs: 0,
      fat: 0
    },

    // 目标完成度
    goalProgress: 0,

    // 弹窗状态
    showAddOptions: false,
    currentMealType: '',

    // 餐次状态
    mealStatus: {
      breakfast: false,
      lunch: false,
      dinner: false,
      snack: false
    },
    sportStatus: false,

    // 加载状态
    loading: true,
    refreshing: false
  },

  onLoad() {
    this.setData({
      todayDate: getTodayDate()
    });
    this.initData();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  onReady() {
    // 页面渲染完成后绘制圆环
    setTimeout(() => {
      this.drawProgressRing(this.data.calorieProgress);
    }, 100);
  },

  onPullDownRefresh() {
    this.setData({ refreshing: true });
    this.refreshData().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 初始化数据
  async initData() {
    try {
      showLoading('加载中...');
      await this.loadUserProfile();
      await this.loadTodayData();
      this.calculateDashboardData();
    } catch (error) {
      console.error('初始化数据失败:', error);
      showError('数据加载失败');
    } finally {
      hideLoading();
      this.setData({ loading: false });
    }
  },

  // 刷新数据
  async refreshData() {
    try {
      await this.loadTodayData();
      this.calculateDashboardData();
    } catch (error) {
      console.error('刷新数据失败:', error);
      showError('刷新失败');
    }
  },

  // 加载用户档案
  async loadUserProfile() {
    try {
      const profile = await userAPI.getUserProfile();
      this.setData({ userProfile: profile });

      // 计算基础数据
      const bmi = calculateBMI(profile.weight, profile.height);
      const bmiStatus = getBMIStatus(bmi);
      const bmr = calculateBMR(profile.weight, profile.height, profile.age, profile.gender);
      const tdee = calculateTDEE(bmr, profile.activityLevel);
      const targetCalories = calculateWeightLossCalories(tdee, profile.goals?.weightLossRate || 0.5);

      this.setData({
        bmi,
        bmiStatus,
        bmr,
        tdee,
        targetCalories
      });
    } catch (error) {
      console.error('加载用户档案失败:', error);
      // 如果没有用户档案，使用默认数据进行演示
      const defaultProfile = {
        height: 165,
        weight: 60,
        targetWeight: 55,
        age: 25,
        gender: 'female',
        activityLevel: 'light'
      };

      this.setData({ userProfile: defaultProfile });

      const bmi = calculateBMI(defaultProfile.weight, defaultProfile.height);
      const bmiStatus = getBMIStatus(bmi);
      const bmr = calculateBMR(defaultProfile.weight, defaultProfile.height, defaultProfile.age, defaultProfile.gender);
      const tdee = calculateTDEE(bmr, defaultProfile.activityLevel);
      const targetCalories = calculateWeightLossCalories(tdee, 0.5);

      this.setData({
        bmi,
        bmiStatus,
        bmr,
        tdee,
        targetCalories
      });
    }
  },

  // 加载今日数据
  async loadTodayData() {
    const today = this.data.todayDate;

    try {
      // 并行加载饮食和运动数据
      const [foodRecords, sportRecords] = await Promise.all([
        foodAPI.getFoodRecords(today).catch(() => []),
        sportAPI.getSportRecords(today).catch(() => [])
      ]);

      // 计算今日营养摄入
      let totalCalories = 0;
      let totalProtein = 0;
      let totalCarbs = 0;
      let totalFat = 0;

      foodRecords.forEach(record => {
        if (record.foods && Array.isArray(record.foods)) {
          record.foods.forEach(food => {
            totalCalories += food.calories || 0;
            totalProtein += food.protein || 0;
            totalCarbs += food.carbs || 0;
            totalFat += food.fat || 0;
          });
        }
      });

      // 计算今日运动消耗
      let totalSportCalories = 0;
      sportRecords.forEach(record => {
        totalSportCalories += record.calories || 0;
      });

      // 计算餐次状态
      const mealStatus = {
        breakfast: false,
        lunch: false,
        dinner: false,
        snack: false
      };

      foodRecords.forEach(record => {
        if (record.mealType && record.foods && record.foods.length > 0) {
          mealStatus[record.mealType] = true;
        }
      });

      this.setData({
        todayNutrition: {
          calories: totalCalories,
          protein: totalProtein,
          carbs: totalCarbs,
          fat: totalFat
        },
        todaySportCalories: totalSportCalories,
        mealStatus: mealStatus,
        sportStatus: totalSportCalories > 0
      });
    } catch (error) {
      console.error('加载今日数据失败:', error);
      // 使用模拟数据进行演示
      this.setData({
        todayNutrition: {
          calories: 1200,
          protein: 60,
          carbs: 150,
          fat: 40
        },
        todaySportCalories: 300
      });
    }
  },

  // 计算仪表盘数据
  calculateDashboardData() {
    const { todayNutrition, todaySportCalories, targetCalories, userProfile } = this.data;

    // 计算剩余热量（考虑运动消耗）
    const netCalories = todayNutrition.calories - todaySportCalories;
    const remainingCalories = targetCalories - netCalories;
    const calorieProgress = Math.min((netCalories / targetCalories) * 100, 100);

    // 获取状态颜色
    const statusColor = getCalorieStatusColor(netCalories, targetCalories);

    // 计算营养素占比
    const totalNutrientCalories =
      (todayNutrition.protein * 4) +
      (todayNutrition.carbs * 4) +
      (todayNutrition.fat * 9);

    let nutrientRatio = { protein: 0, carbs: 0, fat: 0 };
    if (totalNutrientCalories > 0) {
      nutrientRatio = {
        protein: Math.round(((todayNutrition.protein * 4) / totalNutrientCalories) * 100),
        carbs: Math.round(((todayNutrition.carbs * 4) / totalNutrientCalories) * 100),
        fat: Math.round(((todayNutrition.fat * 9) / totalNutrientCalories) * 100)
      };
    }

    // 计算目标完成度
    let goalProgress = 0;
    if (userProfile && userProfile.targetWeight) {
      const totalWeightLoss = userProfile.weight - userProfile.targetWeight;
      const currentProgress = userProfile.weight - userProfile.weight; // 这里需要获取最新体重
      goalProgress = totalWeightLoss > 0 ? (currentProgress / totalWeightLoss) * 100 : 0;
    }

    this.setData({
      remainingCalories,
      calorieProgress,
      statusColor,
      nutrientRatio,
      goalProgress
    });

    // 绘制圆环进度条
    this.drawProgressRing(calorieProgress);
  },

  // 绘制圆环进度条
  drawProgressRing(progress) {
    const ctx = wx.createCanvasContext('progressCanvas', this);
    const centerX = 65; // 130rpx / 2 (canvas实际尺寸的一半)
    const centerY = 65;
    const radius = 50;
    const lineWidth = 8;

    // 清除画布
    ctx.clearRect(0, 0, 130, 130);

    // 绘制背景圆环
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.setStrokeStyle('#f1f5f9');
    ctx.setLineWidth(lineWidth);
    ctx.stroke();

    // 绘制进度圆环（逆时针）
    if (progress > 0) {
      ctx.beginPath();
      // 从12点位置开始，逆时针绘制
      const startAngle = -Math.PI / 2; // 12点位置
      const endAngle = startAngle - (progress / 100) * 2 * Math.PI; // 逆时针
      ctx.arc(centerX, centerY, radius, startAngle, endAngle, true); // true表示逆时针
      ctx.setStrokeStyle('#2563eb');
      ctx.setLineWidth(lineWidth);
      ctx.setLineCap('round');
      ctx.stroke();
    }

    ctx.draw();
  },

  // 快捷操作 - 拍照识别
  onTakePhoto() {
    wx.navigateTo({
      url: '/pages/camera/camera'
    });
  },

  // 快捷操作 - 记录体重
  onRecordWeight() {
    wx.showModal({
      title: '记录体重',
      editable: true,
      placeholderText: '请输入体重(kg)',
      success: async (res) => {
        if (res.confirm && res.content) {
          const weight = parseFloat(res.content);
          if (weight > 0 && weight < 300) {
            try {
              showLoading('记录中...');
              await userAPI.addWeightRecord(weight);
              // 更新用户档案中的体重
              const updatedProfile = { ...this.data.userProfile, weight };
              this.setData({ userProfile: updatedProfile });
              this.calculateDashboardData();
              wx.showToast({
                title: '记录成功',
                icon: 'success'
              });
            } catch (error) {
              showError('记录失败');
            } finally {
              hideLoading();
            }
          } else {
            showError('请输入有效的体重');
          }
        }
      }
    });
  },

  // 快捷操作 - 记录饮食
  onRecordFood() {
    wx.navigateTo({
      url: '/pages/food/food'
    });
  },

  // 快捷操作 - 记录运动
  onRecordSport() {
    wx.navigateTo({
      url: '/pages/sport/sport'
    });
  },

  // 查看详细报告
  onViewReport() {
    wx.switchTab({
      url: '/pages/report/report'
    });
  },

  // 查看个人中心
  onViewProfile() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    });
  },

  // 添加餐次
  onAddMeal(e) {
    const mealType = e.currentTarget.dataset.meal;
    this.setData({
      currentMealType: mealType,
      showAddOptions: true
    });
  },

  // 添加运动
  onAddSport() {
    this.onRecordSport();
  },

  // 显示添加选项弹窗
  onShowAddOptions() {
    this.setData({
      showAddOptions: true
    });
  },

  // 隐藏添加选项弹窗
  onHideAddOptions() {
    this.setData({
      showAddOptions: false,
      currentMealType: ''
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击弹窗内容时关闭弹窗
  },

  // 拍照识别
  onTakePhoto() {
    this.onHideAddOptions();
    const mealType = this.data.currentMealType;
    const url = mealType ? `/pages/camera/camera?meal=${mealType}` : '/pages/camera/camera';
    wx.navigateTo({
      url: url
    });
  },

  // 语音记录
  onVoiceRecord() {
    this.onHideAddOptions();
    wx.showToast({
      title: '语音功能开发中',
      icon: 'none'
    });
  },

  // 打开食物库
  onOpenFoodDatabase() {
    this.onHideAddOptions();
    const mealType = this.data.currentMealType;
    const url = mealType ? `/pages/food-database/food-database?meal=${mealType}` : '/pages/food-database/food-database';
    wx.navigateTo({
      url: url
    });
  }

});
