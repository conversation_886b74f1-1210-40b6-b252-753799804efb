<!-- 智食派首页 - 智能仪表盘 -->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="container">
    <!-- 个人档案卡片 -->
    <view class="card gradient-primary profile-card">
      <view class="profile-header">
        <view class="profile-info">
          <view class="profile-avatar">
            <text class="iconfont icon-wode profile-avatar-icon"></text>
          </view>
          <view class="profile-details">
            <view class="profile-name">智食派用户</view>
            <view class="profile-status">减重进行中</view>
          </view>
        </view>
        <view class="profile-weight">
          <view class="current-weight">{{userProfile.weight}}kg</view>
          <view class="target-weight">目标: {{userProfile.targetWeight}}kg</view>
        </view>
      </view>

      <view class="profile-metrics">
        <view class="metric-item">
          <view class="metric-value">{{bmi}}</view>
          <view class="metric-label">BMI</view>
        </view>
        <view class="metric-item">
          <view class="metric-value">{{bmr}}</view>
          <view class="metric-label">基础代谢</view>
        </view>
        <view class="metric-item">
          <view class="metric-value">{{goalProgress}}%</view>
          <view class="metric-label">目标完成</view>
        </view>
      </view>
    </view>
  
    <!-- 今日热量平衡 -->
    <view class="card">
      <view class="card-title">
        <text class="iconfont icon-tijianbaogao card-title-icon"></text>
        今日热量平衡
      </view>

      <!-- 激励文字 -->
      <view class="motivation-section">
        <view class="motivation-text">{{motivationText}}</view>
        <view class="motivation-divider"></view>
      </view>

      <!-- 热量平衡主体 -->
      <view class="calorie-balance-container">
        <!-- 左侧数据：饮食摄入 -->
        <view class="balance-side-data">
          <view class="side-value">{{todayNutrition.calories}}<text class="side-unit">千卡</text></view>
          <view class="side-label">饮食摄入</view>
        </view>

        <!-- 中心圆环显示区 -->
        <view class="balance-center">
          <!-- 圆环进度条 -->
          <view class="circle-progress-container">
            <!-- Canvas圆环 -->
            <canvas
              canvas-id="progressCanvas"
              class="progress-canvas"
              style="width: 320rpx; height: 320rpx;"
            ></canvas>

            <!-- 中心内容 -->
            <view class="circle-content">
              <view class="center-value">{{remainingCalories >= 0 ? remainingCalories : 0}}</view>
              <view class="center-subtitle">还可以吃</view>
              <view class="center-desc">推荐摄入{{targetCalories}}千卡</view>
            </view>
          </view>
        </view>

        <!-- 右侧数据：运动消耗 -->
        <view class="balance-side-data">
          <view class="side-value">{{todaySportCalories}}<text class="side-unit">千卡</text></view>
          <view class="side-label">运动消耗</view>
        </view>
      </view>

      <!-- 底部功能按钮区 -->
      <view class="meal-actions">
        <view class="meal-action-item" bindtap="onAddMeal" data-meal="breakfast">
          <text class="iconfont icon-zaocan meal-icon"></text>
          <view class="action-label">
            <text class="status-dot {{mealStatus.breakfast ? 'completed' : 'pending'}}">{{mealStatus.breakfast ? '●' : '+'}}</text>
            <text class="label-text">早餐</text>
          </view>
        </view>
        <view class="meal-action-item" bindtap="onAddMeal" data-meal="lunch">
          <text class="iconfont icon-wucan meal-icon"></text>
          <view class="action-label">
            <text class="status-dot {{mealStatus.lunch ? 'completed' : 'pending'}}">{{mealStatus.lunch ? '●' : '+'}}</text>
            <text class="label-text">午餐</text>
          </view>
        </view>
        <view class="meal-action-item" bindtap="onAddMeal" data-meal="dinner">
          <text class="iconfont icon-wancan meal-icon"></text>
          <view class="action-label">
            <text class="status-dot {{mealStatus.dinner ? 'completed' : 'pending'}}">{{mealStatus.dinner ? '●' : '+'}}</text>
            <text class="label-text">晚餐</text>
          </view>
        </view>
        <view class="meal-action-item" bindtap="onAddMeal" data-meal="snack">
          <text class="iconfont icon-ewaijiacan meal-icon"></text>
          <view class="action-label">
            <text class="status-dot {{mealStatus.snack ? 'completed' : 'pending'}}">{{mealStatus.snack ? '●' : '+'}}</text>
            <text class="label-text">加餐</text>
          </view>
        </view>
        <view class="meal-action-item" bindtap="onAddSport">
          <text class="iconfont icon-shouye1 meal-icon"></text>
          <view class="action-label">
            <text class="status-dot {{sportStatus ? 'completed' : 'pending'}}">{{sportStatus ? '●' : '+'}}</text>
            <text class="label-text">运动</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加选项弹窗 -->
    <view wx:if="{{showAddOptions}}" class="add-options-overlay" bindtap="onHideAddOptions">
      <view class="add-options-container" catchtap="stopPropagation">
        <view class="add-option" bindtap="onTakePhoto">
          <text class="iconfont icon-paizhao option-icon"></text>
          <text class="option-text">拍照识别</text>
        </view>
        <view class="add-option" bindtap="onVoiceRecord">
          <text class="iconfont icon-yuyin option-icon"></text>
          <text class="option-text">语音记录</text>
        </view>
        <view class="add-option" bindtap="onOpenFoodDatabase">
          <text class="iconfont icon-gerendangan option-icon"></text>
          <text class="option-text">食物库</text>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions-grid">
      <view class="quick-action-card" bindtap="onTakePhoto">
        <view class="action-icon-container primary">
          <text class="iconfont icon-paizhao action-icon"></text>
        </view>
        <view class="action-title">拍照识别</view>
        <view class="action-subtitle">AI智能识别食物</view>
      </view>

      <view class="smart-weight-card" bindtap="onSmartWeightRecord">
        <!-- 卡片头部 -->
        <view class="weight-card-header">
          <view class="weight-card-title">
            <text class="iconfont icon-tizhong weight-title-icon"></text>
            <text class="weight-title-text">智能体重记录</text>
          </view>
          <view class="weight-settings-btn" bindtap="onWeightSettings" catchtap="stopPropagation">
            <text class="iconfont icon-qushitu settings-icon"></text>
          </view>
        </view>

        <!-- 卡片内容 -->
        <view class="weight-card-content">
          <!-- 正常状态 -->
          <view wx:if="{{weightStatus.mode === 'normal'}}" class="weight-normal-content">
            <view class="weight-current">
              <text class="weight-icon">📊</text>
              <text class="weight-text">当前：{{weightStatus.currentWeight}}kg</text>
            </view>
            <view class="weight-change">
              <text class="change-icon">{{weightStatus.changeIcon}}</text>
              <text class="change-text">较上次：{{weightStatus.changeText}} ({{weightStatus.daysSince}}天前)</text>
            </view>
            <view class="weight-status">
              <text class="status-icon">💡</text>
              <text class="status-text">状态：{{weightStatus.statusText}}</text>
            </view>
            <view class="weight-next">
              <text class="next-icon">⏰</text>
              <text class="next-text">下次：{{weightStatus.nextReminder}}</text>
            </view>
          </view>

          <!-- 提醒状态 -->
          <view wx:elif="{{weightStatus.mode === 'remind'}}" class="weight-remind-content">
            <view class="weight-remind-title">
              <text class="remind-icon">⏰</text>
              <text class="remind-text">该测量体重了！</text>
            </view>
            <view class="weight-last">
              <text class="last-icon">📊</text>
              <text class="last-text">上次：{{weightStatus.lastWeight}}kg ({{weightStatus.daysSince}}天前)</text>
            </view>
            <view class="weight-suggest">
              <text class="suggest-icon">💡</text>
              <text class="suggest-text">建议：保持规律测量习惯</text>
            </view>
            <view class="weight-action">
              <text class="action-icon">🎯</text>
              <text class="action-text">点击立即记录</text>
            </view>
          </view>

          <!-- 异常波动状态 -->
          <view wx:elif="{{weightStatus.mode === 'abnormal'}}" class="weight-abnormal-content">
            <view class="weight-current">
              <text class="weight-icon">📊</text>
              <text class="weight-text">当前：{{weightStatus.currentWeight}}kg</text>
            </view>
            <view class="weight-warning">
              <text class="warning-icon">⚠️</text>
              <text class="warning-text">较上次：{{weightStatus.changeText}} ({{weightStatus.daysSince}}天前)</text>
            </view>
            <view class="weight-status">
              <text class="status-icon">💡</text>
              <text class="status-text">状态：波动较大，请关注</text>
            </view>
            <view class="weight-advice">
              <text class="advice-icon">📞</text>
              <text class="advice-text">建议咨询专业人士</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 今日运动 -->
    <view class="card">
      <view class="card-title">
        <text class="iconfont icon-shouye1 card-title-icon"></text>
        今日运动
      </view>

      <view class="sport-overview">
        <view class="sport-info">
          <view class="sport-icon-container">
            <text class="iconfont icon-xinzengtubiao-03 sport-fire-icon"></text>
          </view>
          <view class="sport-details">
            <view class="sport-calories">消耗 {{todaySportCalories}} kcal</view>
            <view class="sport-activity">{{todaySportCalories > 0 ? '已完成运动' : '暂无运动记录'}}</view>
          </view>
        </view>
        <view class="sport-action">
          <view class="add-sport-btn" bindtap="onRecordSport">添加运动</view>
        </view>
      </view>
    </view>

    <!-- 体重设置弹窗 -->
    <view wx:if="{{showWeightSettings}}" class="weight-settings-overlay" bindtap="onHideWeightSettings">
      <view class="weight-settings-container" catchtap="stopPropagation">
        <view class="settings-header">
          <view class="settings-title">体重记录设置</view>
          <view class="settings-close" bindtap="onHideWeightSettings">
            <text class="iconfont icon-qushitu close-icon"></text>
          </view>
        </view>

        <view class="settings-content">
          <!-- 提醒频率 -->
          <view class="setting-item">
            <view class="setting-label">提醒频率</view>
            <view class="setting-options">
              <view
                wx:for="{{reminderFrequencyOptions}}"
                wx:key="value"
                class="option-item {{weightSettings.reminderFrequency === item.value ? 'selected' : ''}}"
                bindtap="onSelectReminderFrequency"
                data-value="{{item.value}}"
              >
                {{item.label}}
              </view>
            </view>
          </view>

          <!-- 正常波动范围 -->
          <view class="setting-item">
            <view class="setting-label">正常波动范围</view>
            <view class="setting-options">
              <view
                wx:for="{{normalRangeOptions}}"
                wx:key="value"
                class="option-item {{weightSettings.normalRange === item.value ? 'selected' : ''}}"
                bindtap="onSelectNormalRange"
                data-value="{{item.value}}"
              >
                {{item.label}}
              </view>
            </view>
            <view class="setting-desc">超出范围会有特殊提示</view>
          </view>

          <!-- 提醒时间 -->
          <view class="setting-item">
            <view class="setting-label">提醒时间</view>
            <view class="time-picker-container">
              <picker
                mode="time"
                value="{{weightSettings.reminderTime}}"
                bindchange="onReminderTimeChange"
                class="time-picker"
              >
                <view class="time-display">{{weightSettings.reminderTime}}</view>
              </picker>
            </view>
          </view>

          <!-- 显示方式 -->
          <view class="setting-item">
            <view class="setting-label">显示方式</view>
            <view class="setting-options">
              <view
                wx:for="{{displayModeOptions}}"
                wx:key="value"
                class="option-item {{weightSettings.displayMode === item.value ? 'selected' : ''}}"
                bindtap="onSelectDisplayMode"
                data-value="{{item.value}}"
              >
                {{item.label}}
              </view>
            </view>
          </view>
        </view>

        <view class="settings-footer">
          <view class="save-btn" bindtap="onSaveWeightSettings">保存设置</view>
        </view>
      </view>
    </view>

    <!-- 体重输入弹窗 -->
    <view wx:if="{{showWeightInput}}" class="weight-input-overlay" bindtap="onHideWeightInput">
      <view class="weight-input-container" catchtap="stopPropagation">
        <view class="input-header">
          <view class="input-title">{{weightInputTitle}}</view>
          <view wx:if="{{weightInputSubtitle}}" class="input-subtitle">{{weightInputSubtitle}}</view>
        </view>

        <view class="input-content">
          <view class="weight-input-field">
            <input
              type="digit"
              placeholder="{{weightInputPlaceholder}}"
              value="{{inputWeight}}"
              bindinput="onWeightInput"
              class="weight-input"
              focus="{{weightInputFocus}}"
            />
            <text class="weight-unit">kg</text>
          </view>
        </view>

        <view class="input-footer">
          <view class="cancel-btn" bindtap="onHideWeightInput">取消</view>
          <view class="confirm-btn" bindtap="onConfirmWeight">确认记录</view>
        </view>
      </view>
    </view>


  </view>
</view>
