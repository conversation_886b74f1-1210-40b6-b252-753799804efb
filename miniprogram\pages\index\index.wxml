<!-- 智食派首页 - 智能仪表盘 -->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="container">
    <!-- 个人档案卡片 -->
    <view class="card gradient-primary profile-card">
      <view class="profile-header">
        <view class="profile-info">
          <view class="profile-avatar">
            <text class="iconfont icon-wode profile-avatar-icon"></text>
          </view>
          <view class="profile-details">
            <view class="profile-name">智食派用户</view>
            <view class="profile-status">减重进行中</view>
          </view>
        </view>
        <view class="profile-weight">
          <view class="current-weight">{{userProfile.weight}}kg</view>
          <view class="target-weight">目标: {{userProfile.targetWeight}}kg</view>
        </view>
      </view>

      <view class="profile-metrics">
        <view class="metric-item">
          <view class="metric-value">{{bmi}}</view>
          <view class="metric-label">BMI</view>
        </view>
        <view class="metric-item">
          <view class="metric-value">{{bmr}}</view>
          <view class="metric-label">基础代谢</view>
        </view>
        <view class="metric-item">
          <view class="metric-value">{{goalProgress}}%</view>
          <view class="metric-label">目标完成</view>
        </view>
      </view>
    </view>

    <!-- 今日热量平衡 -->
    <view class="card">
      <view class="card-title">
        <text class="iconfont icon-tijianbaogao card-title-icon"></text>
        今日热量平衡
      </view>

      <!-- 热量平衡主体 -->
      <view class="calorie-balance-container">
        <!-- 左侧数据：饮食摄入 -->
        <view class="balance-side-data">
          <view class="side-value">{{todayNutrition.calories}}<text class="side-unit">千卡</text></view>
          <view class="side-label">饮食摄入</view>
        </view>

        <!-- 中心圆环显示区 -->
        <view class="balance-center">
          <!-- 圆环进度条 -->
          <view class="circle-progress-container">
            <!-- Canvas圆环 -->
            <canvas
              canvas-id="progressCanvas"
              class="progress-canvas"
              style="width: 260rpx; height: 260rpx;"
            ></canvas>

            <!-- 中心内容 -->
            <view class="circle-content">
              <view class="center-value">{{remainingCalories >= 0 ? remainingCalories : 0}}</view>
              <view class="center-subtitle">还可以吃</view>
              <view class="center-desc">推荐摄入{{targetCalories}}千卡</view>
            </view>
          </view>
        </view>

        <!-- 右侧数据：运动消耗 -->
        <view class="balance-side-data">
          <view class="side-value">{{todaySportCalories}}<text class="side-unit">千卡</text></view>
          <view class="side-label">运动消耗</view>
        </view>
      </view>

      <!-- 底部功能按钮区 -->
      <view class="meal-actions">
        <view class="meal-action-item" bindtap="onAddMeal" data-meal="breakfast">
          <text class="iconfont icon-zaocan meal-icon"></text>
          <view class="action-label">
            <text class="status-dot {{mealStatus.breakfast ? 'completed' : 'pending'}}">{{mealStatus.breakfast ? '●' : '+'}}</text>
            <text class="label-text">早餐</text>
          </view>
        </view>
        <view class="meal-action-item" bindtap="onAddMeal" data-meal="lunch">
          <text class="iconfont icon-wucan meal-icon"></text>
          <view class="action-label">
            <text class="status-dot {{mealStatus.lunch ? 'completed' : 'pending'}}">{{mealStatus.lunch ? '●' : '+'}}</text>
            <text class="label-text">午餐</text>
          </view>
        </view>
        <view class="meal-action-item" bindtap="onAddMeal" data-meal="dinner">
          <text class="iconfont icon-wancan meal-icon"></text>
          <view class="action-label">
            <text class="status-dot {{mealStatus.dinner ? 'completed' : 'pending'}}">{{mealStatus.dinner ? '●' : '+'}}</text>
            <text class="label-text">晚餐</text>
          </view>
        </view>
        <view class="meal-action-item" bindtap="onAddMeal" data-meal="snack">
          <text class="iconfont icon-ewaijiacan meal-icon"></text>
          <view class="action-label">
            <text class="status-dot {{mealStatus.snack ? 'completed' : 'pending'}}">{{mealStatus.snack ? '●' : '+'}}</text>
            <text class="label-text">加餐</text>
          </view>
        </view>
        <view class="meal-action-item" bindtap="onAddSport">
          <text class="iconfont icon-shouye1 meal-icon"></text>
          <view class="action-label">
            <text class="status-dot {{sportStatus ? 'completed' : 'pending'}}">{{sportStatus ? '●' : '+'}}</text>
            <text class="label-text">运动</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加选项弹窗 -->
    <view wx:if="{{showAddOptions}}" class="add-options-overlay" bindtap="onHideAddOptions">
      <view class="add-options-container" catchtap="stopPropagation">
        <view class="add-option" bindtap="onTakePhoto">
          <text class="iconfont icon-paizhao option-icon"></text>
          <text class="option-text">拍照识别</text>
        </view>
        <view class="add-option" bindtap="onVoiceRecord">
          <text class="iconfont icon-yuyin option-icon"></text>
          <text class="option-text">语音记录</text>
        </view>
        <view class="add-option" bindtap="onOpenFoodDatabase">
          <text class="iconfont icon-gerendangan option-icon"></text>
          <text class="option-text">食物库</text>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions-grid">
      <view class="quick-action-card" bindtap="onTakePhoto">
        <view class="action-icon-container primary">
          <text class="iconfont icon-paizhao action-icon"></text>
        </view>
        <view class="action-title">拍照识别</view>
        <view class="action-subtitle">AI智能识别食物</view>
      </view>

      <view class="quick-action-card" bindtap="onRecordWeight">
        <view class="action-icon-container success">
          <text class="iconfont icon-tizhong action-icon"></text>
        </view>
        <view class="action-title">记录体重</view>
        <view class="action-subtitle">追踪体重变化</view>
      </view>
    </view>

    <!-- 今日运动 -->
    <view class="card">
      <view class="card-title">
        <text class="iconfont icon-shouye1 card-title-icon"></text>
        今日运动
      </view>

      <view class="sport-overview">
        <view class="sport-info">
          <view class="sport-icon-container">
            <text class="iconfont icon-xinzengtubiao-03 sport-fire-icon"></text>
          </view>
          <view class="sport-details">
            <view class="sport-calories">消耗 {{todaySportCalories}} kcal</view>
            <view class="sport-activity">{{todaySportCalories > 0 ? '已完成运动' : '暂无运动记录'}}</view>
          </view>
        </view>
        <view class="sport-action">
          <view class="add-sport-btn" bindtap="onRecordSport">添加运动</view>
        </view>
      </view>
    </view>


  </view>
</view>
