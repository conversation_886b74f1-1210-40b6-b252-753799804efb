<!-- 智食派首页 - 智能仪表盘 -->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="container">
    <!-- 个人档案卡片 -->
    <view class="card gradient-primary profile-card">
      <view class="profile-header">
        <view class="profile-info">
          <view class="profile-avatar">
            <text class="iconfont icon-wode profile-avatar-icon"></text>
          </view>
          <view class="profile-details">
            <view class="profile-name">智食派用户</view>
            <view class="profile-status">减重进行中</view>
          </view>
        </view>
        <view class="profile-weight">
          <view class="current-weight">{{userProfile.weight}}kg</view>
          <view class="target-weight">目标: {{userProfile.targetWeight}}kg</view>
        </view>
      </view>

      <view class="profile-metrics">
        <view class="metric-item">
          <view class="metric-value">{{bmi}}</view>
          <view class="metric-label">BMI</view>
        </view>
        <view class="metric-item">
          <view class="metric-value">{{bmr}}</view>
          <view class="metric-label">基础代谢</view>
        </view>
        <view class="metric-item">
          <view class="metric-value">{{goalProgress}}%</view>
          <view class="metric-label">目标完成</view>
        </view>
      </view>
    </view>

    <!-- 今日热量平衡 -->
    <view class="card">
      <view class="card-title">
        <text class="iconfont icon-tijianbaogao card-title-icon"></text>
        今日热量平衡
      </view>

      <!-- 热量进度 -->
      <view class="calorie-overview">
        <view class="calorie-header">
          <view class="calorie-consumed-label">已摄入</view>
          <view class="calorie-main">
            <text class="calorie-consumed">{{todayNutrition.calories}}</text>
            <text class="calorie-separator"> / </text>
            <text class="calorie-target">{{targetCalories}}</text>
            <text class="calorie-unit"> kcal</text>
          </view>
        </view>

        <view class="progress-bar mt-16">
          <view class="progress-fill" style="width: {{calorieProgress}}%; background: linear-gradient(90deg, #10b981, #2563eb)"></view>
        </view>

        <view class="calorie-status">
          <view class="status-left">剩余 {{remainingCalories}} kcal</view>
          <view class="status-right" style="color: {{statusColor}}">
            {{remainingCalories > 200 ? '安全区间' : remainingCalories >= 0 ? '注意控制' : '已超标'}}
          </view>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions-grid">
      <view class="quick-action-card" bindtap="onTakePhoto">
        <view class="action-icon-container primary">
          <text class="iconfont icon-paizhao action-icon"></text>
        </view>
        <view class="action-title">拍照识别</view>
        <view class="action-subtitle">AI智能识别食物</view>
      </view>

      <view class="quick-action-card" bindtap="onRecordWeight">
        <view class="action-icon-container success">
          <text class="iconfont icon-tizhong action-icon"></text>
        </view>
        <view class="action-title">记录体重</view>
        <view class="action-subtitle">追踪体重变化</view>
      </view>
    </view>

    <!-- 今日运动 -->
    <view class="card">
      <view class="card-title">
        <text class="iconfont icon-shouye1 card-title-icon"></text>
        今日运动
      </view>

      <view class="sport-overview">
        <view class="sport-info">
          <view class="sport-icon-container">
            <text class="iconfont icon-xinzengtubiao-03 sport-fire-icon"></text>
          </view>
          <view class="sport-details">
            <view class="sport-calories">消耗 {{todaySportCalories}} kcal</view>
            <view class="sport-activity">{{todaySportCalories > 0 ? '已完成运动' : '暂无运动记录'}}</view>
          </view>
        </view>
        <view class="sport-action">
          <view class="add-sport-btn" bindtap="onRecordSport">添加运动</view>
        </view>
      </view>
    </view>


  </view>
</view>
