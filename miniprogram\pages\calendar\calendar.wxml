<!-- 日历记录页面 -->
<view class="page-container">
  <view class="container">
    <!-- 月份导航 -->
    <view class="month-nav">
      <view class="nav-btn" bindtap="onPrevMonth">
        <text class="iconfont icon-sousuo nav-icon"></text>
      </view>
      <view class="month-title">{{currentMonth}}</view>
      <view class="nav-btn" bindtap="onNextMonth">
        <text class="iconfont icon-sousuo nav-icon"></text>
      </view>
    </view>

    <!-- 星期标题 -->
    <view class="week-header">
      <view class="week-day" wx:for="{{weekDays}}" wx:key="*this">{{item}}</view>
    </view>

    <!-- 日历网格 -->
    <view class="calendar-grid">
      <view 
        class="calendar-day {{item.isCurrentMonth ? '' : 'other-month'}} {{item.isToday ? 'today' : ''}} {{item.hasRecord ? 'has-record' : ''}}"
        wx:for="{{calendarDays}}" 
        wx:key="date"
        bindtap="onSelectDay"
        data-date="{{item.date}}"
      >
        <view class="day-number">{{item.day}}</view>
        <view wx:if="{{item.hasRecord}}" class="record-indicator">
          <view class="calorie-dot" wx:if="{{item.calories}}"></view>
          <view class="weight-dot" wx:if="{{item.weight}}"></view>
        </view>
      </view>
    </view>

    <!-- 选中日期的详情 -->
    <view wx:if="{{selectedDate}}" class="day-detail">
      <view class="detail-header">
        <view class="detail-date">{{selectedDate.dateStr}}</view>
        <view class="edit-btn" bindtap="onEditRecord">编辑</view>
      </view>

      <view class="detail-content">
        <view class="detail-item">
          <view class="detail-label">
            <text class="iconfont icon-xinzengtubiao-03 detail-icon"></text>
            热量摄入
          </view>
          <view class="detail-value">{{selectedDate.calories || 0}} kcal</view>
        </view>

        <view class="detail-item">
          <view class="detail-label">
            <text class="iconfont icon-tizhong detail-icon"></text>
            体重记录
          </view>
          <view class="detail-value">{{selectedDate.weight || '未记录'}} {{selectedDate.weight ? 'kg' : ''}}</view>
        </view>
      </view>
    </view>

    <!-- 记录编辑弹窗 -->
    <view wx:if="{{showEditModal}}" class="modal-overlay" bindtap="onCloseModal">
      <view class="modal-content" catchtap="stopPropagation">
        <view class="modal-header">
          <view class="modal-title">记录 {{selectedDate.dateStr}}</view>
          <view class="close-btn" bindtap="onCloseModal">×</view>
        </view>

        <view class="modal-body">
          <view class="input-group">
            <view class="input-label">热量摄入 (kcal)</view>
            <input 
              class="input-field" 
              type="number" 
              placeholder="请输入热量"
              value="{{editData.calories}}"
              bindinput="onCaloriesInput"
            />
          </view>

          <view class="input-group">
            <view class="input-label">体重 (kg)</view>
            <input 
              class="input-field" 
              type="digit" 
              placeholder="请输入体重"
              value="{{editData.weight}}"
              bindinput="onWeightInput"
            />
          </view>
        </view>

        <view class="modal-footer">
          <view class="modal-btn cancel" bindtap="onCloseModal">取消</view>
          <view class="modal-btn confirm" bindtap="onSaveRecord">保存</view>
        </view>
      </view>
    </view>
  </view>
</view>
