<!-- 食物库页面 -->
<view class="page-container">
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-container">
      <view class="search-box">
        <text class="iconfont icon-sousuo search-icon"></text>
        <input 
          class="search-input" 
          placeholder="搜索食物名称"
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
          bindconfirm="onSearch"
        />
        <view wx:if="{{searchKeyword}}" class="clear-btn" bindtap="onClearSearch">×</view>
      </view>
    </view>

    <!-- 分类标签 -->
    <view class="category-tabs">
      <view 
        class="category-tab {{activeCategory === item.key ? 'active' : ''}}"
        wx:for="{{categories}}" 
        wx:key="key"
        bindtap="onSelectCategory"
        data-category="{{item.key}}"
      >
        <text class="iconfont {{item.icon}} category-icon"></text>
        {{item.name}}
      </view>
    </view>

    <!-- 食物列表 -->
    <view class="food-list">
      <view
        class="food-item"
        wx:for="{{filteredFoods}}"
        wx:key="id"
        bindtap="onSelectFood"
        data-food="{{item}}"
      >
        <view class="food-info">
          <image class="food-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="food-details">
            <view class="food-name">{{item.name}}</view>
            <view class="food-desc">{{item.description}}</view>
            <view class="food-nutrition">
              <text class="nutrition-item">热量: {{item.calories}}kcal/100g</text>
              <text class="nutrition-item">蛋白质: {{item.protein}}g</text>
              <text class="nutrition-item">脂肪: {{item.fat}}g</text>
              <text class="nutrition-item">碳水: {{item.carbs}}g</text>
            </view>
          </view>
        </view>
        <view class="food-calories">
          <view class="calories-value">{{item.calories}}</view>
          <view class="calories-unit">kcal/100g</view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{filteredFoods.length === 0}}" class="empty-state">
      <text class="iconfont icon-sousuo empty-icon"></text>
      <view class="empty-text">没有找到相关食物</view>
      <view class="empty-desc">试试其他关键词或选择不同分类</view>
    </view>
  </view>

  <!-- 食物详情弹窗 -->
  <view wx:if="{{showFoodDetail}}" class="modal-overlay" bindtap="onCloseDetail">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <view class="modal-title">{{selectedFood.name}}</view>
        <view class="close-btn" bindtap="onCloseDetail">×</view>
      </view>

      <view class="modal-body">
        <view class="food-detail">
          <view class="detail-section">
            <view class="section-title">营养成分 (每100g)</view>
            <view class="nutrition-grid">
              <view class="nutrition-card">
                <view class="nutrition-value">{{selectedFood.calories}}</view>
                <view class="nutrition-label">热量 (kcal)</view>
              </view>
              <view class="nutrition-card">
                <view class="nutrition-value">{{selectedFood.protein}}</view>
                <view class="nutrition-label">蛋白质 (g)</view>
              </view>
              <view class="nutrition-card">
                <view class="nutrition-value">{{selectedFood.fat}}</view>
                <view class="nutrition-label">脂肪 (g)</view>
              </view>
              <view class="nutrition-card">
                <view class="nutrition-value">{{selectedFood.carbs}}</view>
                <view class="nutrition-label">碳水化合物 (g)</view>
              </view>
            </view>
          </view>

          <view class="detail-section">
            <view class="section-title">食物描述</view>
            <view class="food-description">{{selectedFood.description}}</view>
          </view>

          <view class="detail-section">
            <view class="section-title">常见重量</view>
            <view class="weight-examples">
              <view class="weight-item" wx:for="{{selectedFood.commonWeights}}" wx:key="name">
                <view class="weight-name">{{item.name}}</view>
                <view class="weight-value">约{{item.weight}}g ({{Math.round(selectedFood.calories * item.weight / 100)}}kcal)</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <view class="modal-btn cancel" bindtap="onCloseDetail">关闭</view>
        <view class="modal-btn confirm" bindtap="onAddToMeal">添加到饮食</view>
      </view>
    </view>
  </view>
</view>
