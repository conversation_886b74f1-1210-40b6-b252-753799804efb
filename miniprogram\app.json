{"pages": ["pages/index/index", "pages/food/food", "pages/camera/camera", "pages/result/result", "pages/report/report", "pages/sport/sport", "pages/profile/profile", "pages/calendar/calendar", "pages/food-calendar/food-calendar", "pages/food-database/food-database"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#2563eb", "navigationBarTitleText": "智食派", "navigationBarTextStyle": "white", "backgroundColor": "#f8fafc"}, "tabBar": {"color": "#64748b", "selectedColor": "#2563eb", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "components/icon/shouye.png", "selectedIconPath": "components/icon/shouye.png"}, {"pagePath": "pages/food/food", "text": "饮食", "iconPath": "components/icon/yinshi.png", "selectedIconPath": "components/icon/yinshi.png"}, {"pagePath": "pages/report/report", "text": "报告", "iconPath": "components/icon/baogao.png", "selectedIconPath": "components/icon/baogao.png"}, {"pagePath": "pages/profile/profile", "text": "我的", "iconPath": "components/icon/wode.png", "selectedIconPath": "components/icon/wode.png"}]}, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredPrivateInfos": ["chooseLocation"], "cloud": true, "style": "v2", "componentFramework": "glass-easel", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents"}