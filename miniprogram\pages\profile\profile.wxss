/* 智食派个人中心页面样式 */

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-text {
  color: var(--text-secondary);
  font-size: 32rpx;
}

/* 用户头像和基本信息 */
.profile-header {
  display: flex;
  align-items: center;
  padding: 48rpx 32rpx;
  background: linear-gradient(135deg, var(--primary-color), #3b82f6);
  color: white;
  margin-bottom: 32rpx;
}

.avatar-container {
  margin-right: 32rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 64rpx;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.user-status {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 基础指标卡片 */
.metrics-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin: 0 32rpx 32rpx;
  box-shadow: var(--shadow);
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 32rpx;
  text-align: center;
}

.metric-item {
  padding: 24rpx;
  background: var(--bg-primary);
  border-radius: 16rpx;
}

.metric-value {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.metric-label {
  font-size: 20rpx;
  color: var(--text-secondary);
}

/* 卡片标题 */
.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.edit-btn {
  padding: 12rpx 24rpx;
  background: var(--primary-color);
  color: white;
  border-radius: 16rpx;
  font-size: 24rpx;
}

.edit-btn:active {
  opacity: 0.8;
}

/* 表单样式 */
.form-container {
  padding: 16rpx 0;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: var(--text-primary);
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  background: var(--bg-primary);
  border-radius: 16rpx;
  font-size: 28rpx;
  border: 2rpx solid transparent;
}

.form-input:focus {
  border-color: var(--primary-color);
}

.picker-display {
  padding: 24rpx;
  background: var(--bg-primary);
  border-radius: 16rpx;
  font-size: 28rpx;
  color: var(--text-primary);
}

.form-actions {
  display: flex;
  gap: 24rpx;
  margin-top: 48rpx;
}

.form-btn {
  flex: 1;
  padding: 24rpx;
  text-align: center;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.form-btn.cancel {
  background: var(--bg-primary);
  color: var(--text-secondary);
}

.form-btn.save {
  background: var(--primary-color);
  color: white;
}

.form-btn:active {
  opacity: 0.8;
}

/* 档案显示 */
.profile-display {
  padding: 16rpx 0;
}

.profile-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.profile-item {
  padding: 24rpx;
  background: var(--bg-primary);
  border-radius: 16rpx;
  text-align: center;
}

.profile-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
}

.profile-value {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
}

/* 功能菜单 */
.menu-list {
  margin: 0 32rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: var(--shadow);
}

.menu-item:active {
  background: var(--bg-primary);
}

.menu-icon {
  font-size: 32rpx;
  margin-right: 24rpx;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-primary);
}

.menu-arrow {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 图标样式 */
.avatar-icon {
  font-size: 64rpx;
  color: var(--primary-color);
}

.card-title-icon {
  font-size: 32rpx;
  color: var(--primary-color);
  margin-right: 16rpx;
}

.menu-icon-font {
  font-size: 32rpx;
  color: var(--primary-color);
  margin-right: 24rpx;
}