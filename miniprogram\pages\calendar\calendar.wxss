/* 日历记录页面样式 */
.page-container {
  background-color: #f8fafc;
  min-height: 100vh;
}

.container {
  padding: 32rpx;
}

/* 月份导航 */
.month-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.nav-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  border-radius: 50%;
}

.nav-icon {
  font-size: 24rpx;
  color: white;
}

.month-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
}

/* 星期标题 */
.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.week-day {
  text-align: center;
  font-size: 24rpx;
  color: var(--text-secondary);
  padding: 16rpx 0;
  font-weight: 600;
}

/* 日历网格 */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  margin-bottom: 32rpx;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  position: relative;
  transition: all 0.3s ease;
}

.calendar-day:active {
  transform: scale(0.95);
}

.calendar-day.other-month {
  opacity: 0.3;
}

.calendar-day.today {
  background: var(--primary-color);
  color: white;
}

.calendar-day.has-record {
  background: rgba(37, 99, 235, 0.1);
}

.calendar-day.today.has-record {
  background: var(--primary-color);
}

.day-number {
  font-size: 28rpx;
  font-weight: 600;
}

.record-indicator {
  display: flex;
  gap: 4rpx;
  margin-top: 4rpx;
}

.calorie-dot, .weight-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
}

.calorie-dot {
  background: var(--warning-color);
}

.weight-dot {
  background: var(--success-color);
}

/* 日期详情 */
.day-detail {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  margin-bottom: 32rpx;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.detail-date {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.edit-btn {
  background: var(--primary-color);
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: var(--text-primary);
}

.detail-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
  color: var(--primary-color);
}

.detail-value {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--primary-color);
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f1f5f9;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: var(--text-secondary);
}

.modal-body {
  padding: 32rpx;
}

.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  font-size: 28rpx;
  color: var(--text-primary);
  margin-bottom: 16rpx;
  font-weight: 600;
}

.input-field {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f8fafc;
}

.input-field:focus {
  border-color: var(--primary-color);
  background: white;
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #f1f5f9;
}

.modal-btn {
  flex: 1;
  padding: 32rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
}

.modal-btn.cancel {
  color: var(--text-secondary);
}

.modal-btn.confirm {
  color: var(--primary-color);
  border-left: 2rpx solid #f1f5f9;
}
