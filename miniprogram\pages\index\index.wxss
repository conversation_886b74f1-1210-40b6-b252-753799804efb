/* 智食派首页样式 */

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-text {
  color: var(--text-secondary);
  font-size: 32rpx;
}

/* 个人档案卡片 */
.profile-card {
  margin-bottom: 24rpx;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.profile-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.profile-avatar {
  width: 96rpx;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  font-size: 48rpx;
  color: white;
}

.profile-details {
  flex: 1;
}

.profile-name {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.profile-status {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

.profile-weight {
  text-align: right;
}

.current-weight {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.target-weight {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

.profile-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 32rpx;
  text-align: center;
}

.metric-item {
  text-align: center;
}

.metric-value {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.metric-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 激励文字区域 */
.motivation-section {
  margin-bottom: 32rpx;
}

.motivation-text {
  font-size: 26rpx;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 24rpx;
  line-height: 1.4;
  font-weight: 400;
}

.motivation-divider {
  width: 100%;
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, #e2e8f0 20%, #e2e8f0 80%, transparent 100%);
  margin: 0 auto;
}

/* 热量平衡容器 */
.calorie-balance-container {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: 24rpx;
  padding: 0 16rpx;
}

/* 左右侧数据显示 */
.balance-side-data {
  flex: 0 0 auto;
  text-align: center;
  width: 120rpx;
}

.side-value {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 8rpx;
  line-height: 1;
  white-space: nowrap;
}

.side-unit {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-left: 0;
}

.side-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  font-weight: 400;
  text-align: center;
  line-height: 1.2;
  white-space: nowrap;
}

/* 中心圆环显示区 */
.balance-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 16rpx;
}

.circle-progress-container {
  position: relative;
  width: 320rpx;
  height: 320rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Canvas圆环 */
.progress-canvas {
  position: absolute;
  width: 320rpx;
  height: 320rpx;
}

/* 中心内容 */
.circle-content {
  position: absolute;
  top: 48%;
  left: 48%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200rpx;
}

.center-subtitle {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 12rpx;
  font-weight: 400;
  order: 1;
}

.center-value {
  font-size: 64rpx;
  font-weight: bold;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 12rpx;
  order: 2;
}

.center-desc {
  font-size: 20rpx;
  color: var(--text-secondary);
  line-height: 1.2;
  font-weight: 400;
  order: 3;
}

/* 餐次功能按钮区 */
.meal-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 8rpx 8rpx;
  margin-top: 8rpx;
}

.meal-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  flex: 1;
  max-width: 120rpx;
  transition: all 0.3s ease;
}

.meal-action-item:active {
  transform: scale(0.95);
}

.meal-icon {
  font-size: 48rpx;
  color: var(--primary-color);
}

.action-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 22rpx;
  color: var(--text-primary);
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.status-dot {
  font-size: 20rpx;
  font-weight: bold;
}

.status-dot.completed {
  color: var(--text-primary);
}

.status-dot.pending {
  color: #cbd5e1;
}

.label-text {
  font-size: 22rpx;
  color: var(--text-primary);
  font-weight: 500;
}

/* 添加选项弹窗 */
.add-options-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.add-options-container {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  padding: 48rpx 32rpx;
  width: 100%;
  max-width: 750rpx;
  animation: slideUp 0.3s ease;
}

.add-option {
  display: flex;
  align-items: center;
  padding: 32rpx;
  margin-bottom: 16rpx;
  background: #f8fafc;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.add-option:last-child {
  margin-bottom: 0;
}

.add-option:active {
  transform: scale(0.98);
  background: #e2e8f0;
}

.option-icon {
  font-size: 40rpx;
  color: var(--primary-color);
  margin-right: 32rpx;
}

.option-text {
  font-size: 32rpx;
  color: var(--text-primary);
  font-weight: 500;
}

/* 弹窗动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

/* 营养素分布 */
.nutrient-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.nutrient-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  box-shadow: var(--shadow);
}

.nutrient-card.carbs {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
}

.nutrient-card.protein {
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
}

.nutrient-card.fat {
  background: linear-gradient(135deg, #fffbeb, #fef3c7);
}

.nutrient-icon {
  font-size: 32rpx;
  margin-bottom: 16rpx;
}

.nutrient-label {
  font-size: 20rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
}

.nutrient-percentage {
  font-size: 32rpx;
  font-weight: bold;
}

.nutrient-card.carbs .nutrient-percentage {
  color: #ef4444;
}

.nutrient-card.protein .nutrient-percentage {
  color: #3b82f6;
}

.nutrient-card.fat .nutrient-percentage {
  color: #f59e0b;
}

/* 快捷操作 */
.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin: 0 32rpx 24rpx;
}

.quick-action-card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  text-align: center;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.quick-action-card:active {
  transform: scale(0.95);
}

.action-icon-container {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
}

.action-icon-container.primary {
  background: var(--primary-color);
}

.action-icon-container.success {
  background: var(--success-color);
}

.action-icon {
  font-size: 48rpx;
  color: #ffffff;
}

.action-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.action-subtitle {
  font-size: 20rpx;
  color: var(--text-secondary);
}

/* 智能体重管理卡片 */
.smart-weight-card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  position: relative;
}

.smart-weight-card:active {
  transform: scale(0.95);
}

.weight-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.weight-card-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.weight-title-icon {
  font-size: 32rpx;
  color: var(--primary-color);
}

.weight-title-text {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.weight-settings-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f8fafc;
  transition: all 0.3s ease;
}

.weight-settings-btn:active {
  background: #e2e8f0;
  transform: scale(0.9);
}

.settings-icon {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.weight-card-content {
  min-height: 160rpx;
}

/* 正常状态样式 */
.weight-normal-content,
.weight-remind-content,
.weight-abnormal-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.weight-current,
.weight-change,
.weight-status,
.weight-next,
.weight-remind-title,
.weight-last,
.weight-suggest,
.weight-action,
.weight-warning,
.weight-advice {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 24rpx;
  line-height: 1.4;
}

.weight-icon,
.change-icon,
.status-icon,
.next-icon,
.remind-icon,
.last-icon,
.suggest-icon,
.action-icon,
.warning-icon,
.advice-icon {
  font-size: 24rpx;
  flex-shrink: 0;
}

.weight-text,
.change-text,
.status-text,
.next-text,
.remind-text,
.last-text,
.suggest-text,
.action-text,
.warning-text,
.advice-text {
  color: var(--text-primary);
  font-weight: 500;
}

/* 提醒状态特殊样式 */
.weight-remind-title .remind-text {
  color: #f59e0b;
  font-weight: 600;
  font-size: 26rpx;
}

.weight-action .action-text {
  color: var(--primary-color);
  font-weight: 600;
}

/* 异常状态特殊样式 */
.weight-warning .warning-text {
  color: #ef4444;
  font-weight: 600;
}

.weight-advice .advice-text {
  color: #f59e0b;
  font-weight: 600;
}

/* 体重设置弹窗 */
.weight-settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.weight-settings-container {
  background: white;
  border-radius: 32rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: scaleIn 0.3s ease;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f1f5f9;
}

.settings-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.settings-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f8fafc;
  transition: all 0.3s ease;
}

.settings-close:active {
  background: #e2e8f0;
}

.close-icon {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.settings-content {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.setting-item {
  margin-bottom: 40rpx;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.setting-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.option-item {
  padding: 16rpx 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.option-item.selected {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.option-item:active {
  transform: scale(0.95);
}

.setting-desc {
  font-size: 20rpx;
  color: var(--text-secondary);
  margin-top: 12rpx;
  line-height: 1.4;
}

.time-picker-container {
  display: flex;
  align-items: center;
}

.time-picker {
  flex: 1;
}

.time-display {
  padding: 16rpx 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 20rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  text-align: center;
}

.settings-footer {
  padding: 32rpx;
  border-top: 2rpx solid #f1f5f9;
}

.save-btn {
  width: 100%;
  padding: 24rpx;
  background: var(--primary-color);
  color: white;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.save-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 体重输入弹窗 */
.weight-input-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.weight-input-container {
  background: white;
  border-radius: 32rpx;
  width: 80%;
  max-width: 500rpx;
  animation: scaleIn 0.3s ease;
}

.input-header {
  padding: 32rpx 32rpx 24rpx;
  text-align: center;
  border-bottom: 2rpx solid #f1f5f9;
}

.input-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.input-subtitle {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.input-content {
  padding: 40rpx 32rpx;
}

.weight-input-field {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.weight-input {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-primary);
  text-align: center;
  border: none;
  outline: none;
  background: transparent;
  min-width: 200rpx;
}

.weight-unit {
  font-size: 32rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.input-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f8fafc;
  color: var(--text-secondary);
  border: 2rpx solid #e2e8f0;
}

.cancel-btn:active {
  background: #e2e8f0;
}

.confirm-btn {
  background: var(--primary-color);
  color: white;
}

.confirm-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 弹窗动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 今日运动 */
.sport-overview {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sport-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.sport-icon-container {
  width: 80rpx;
  height: 80rpx;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sport-icon {
  font-size: 32rpx;
  color: var(--success-color);
}

.sport-details {
  flex: 1;
}

.sport-calories {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.sport-activity {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.sport-action {
  margin-left: 24rpx;
}

.add-sport-btn {
  padding: 16rpx 32rpx;
  background: var(--success-color);
  color: white;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.add-sport-btn:active {
  opacity: 0.8;
}

/* 移除底部导航后的容器样式 */
.container {
  padding-bottom: 32rpx;
}

/* 图标样式 */
.profile-avatar-icon {
  font-size: 48rpx;
  color: rgba(255, 255, 255, 0.9);
}

.card-title-icon {
  font-size: 32rpx;
  color: var(--primary-color);
  margin-right: 16rpx;
}

.nutrient-icon {
  font-size: 40rpx;
  margin-bottom: 16rpx;
}

.carbs-icon {
  color: #f59e0b;
}

.protein-icon {
  color: #ef4444;
}

.fat-icon {
  color: #10b981;
}

.action-icon {
  font-size: 48rpx;
  color: #ffffff;
}

.sport-fire-icon {
  font-size: 32rpx;
  color: #ef4444;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .profile-stats {
    gap: 16rpx;
  }

  .stat-value {
    font-size: 24rpx;
  }

  .calorie-consumed {
    font-size: 40rpx;
  }

  .quick-actions {
    gap: 16rpx;
  }

  .action-item {
    padding: 24rpx;
  }

  .action-icon {
    font-size: 40rpx;
  }

  .action-name {
    font-size: 24rpx;
  }
}
