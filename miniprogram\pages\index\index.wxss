/* 智食派首页样式 */

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-text {
  color: var(--text-secondary);
  font-size: 32rpx;
}

/* 个人档案卡片 */
.profile-card {
  margin-bottom: 32rpx;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.profile-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.profile-avatar {
  width: 96rpx;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  font-size: 48rpx;
  color: white;
}

.profile-details {
  flex: 1;
}

.profile-name {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.profile-status {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

.profile-weight {
  text-align: right;
}

.current-weight {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.target-weight {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

.profile-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 32rpx;
  text-align: center;
}

.metric-item {
  text-align: center;
}

.metric-value {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.metric-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 热量平衡容器 */
.calorie-balance-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
  padding: 0 16rpx;
}

/* 左右侧数据显示 */
.balance-side-data {
  flex: 0 0 auto;
  text-align: center;
  width: 100rpx;
}

.side-value {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 8rpx;
  line-height: 1;
}

.side-unit {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-left: 0;
}

.side-label {
  font-size: 20rpx;
  color: var(--text-secondary);
  font-weight: 400;
  text-align: center;
  line-height: 1.2;
}

/* 中心圆环显示区 */
.balance-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 16rpx;
}

.circle-progress-container {
  position: relative;
  width: 260rpx;
  height: 260rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 背景圆环 */
.circle-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 12rpx solid #f1f5f9;
  border-radius: 50%;
  box-sizing: border-box;
}

/* 进度圆环 */
.progress-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(
    from 90deg,
    var(--primary-color) 0deg,
    var(--primary-color) calc(var(--progress, 0) * 3.6deg),
    transparent calc(var(--progress, 0) * 3.6deg),
    transparent 360deg
  );
  mask: radial-gradient(circle at center, transparent 108rpx, black 120rpx);
  -webkit-mask: radial-gradient(circle at center, transparent 108rpx, black 120rpx);
  transition: all 1s ease-out;
  transform: scaleX(-1);
}

/* 中心内容 */
.circle-content {
  position: relative;
  text-align: center;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.center-subtitle {
  font-size: 22rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
  font-weight: 400;
  order: 1;
}

.center-value {
  font-size: 56rpx;
  font-weight: bold;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 12rpx;
  order: 2;
}

.center-desc {
  font-size: 18rpx;
  color: var(--text-secondary);
  line-height: 1.2;
  font-weight: 400;
  order: 3;
}

/* 餐次功能按钮区 */
.meal-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 8rpx 16rpx;
  margin-top: 16rpx;
}

.meal-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  flex: 1;
  max-width: 120rpx;
  transition: all 0.3s ease;
}

.meal-action-item:active {
  transform: scale(0.95);
}

.meal-icon {
  font-size: 36rpx;
  color: var(--primary-color);
}

.action-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 22rpx;
  color: var(--text-primary);
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.status-dot {
  font-size: 20rpx;
  font-weight: bold;
}

.status-dot.completed {
  color: var(--text-primary);
}

.status-dot.pending {
  color: #cbd5e1;
}

.label-text {
  font-size: 22rpx;
  color: var(--text-primary);
  font-weight: 500;
}

/* 添加选项弹窗 */
.add-options-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.add-options-container {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  padding: 48rpx 32rpx;
  width: 100%;
  max-width: 750rpx;
  animation: slideUp 0.3s ease;
}

.add-option {
  display: flex;
  align-items: center;
  padding: 32rpx;
  margin-bottom: 16rpx;
  background: #f8fafc;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.add-option:last-child {
  margin-bottom: 0;
}

.add-option:active {
  transform: scale(0.98);
  background: #e2e8f0;
}

.option-icon {
  font-size: 40rpx;
  color: var(--primary-color);
  margin-right: 32rpx;
}

.option-text {
  font-size: 32rpx;
  color: var(--text-primary);
  font-weight: 500;
}

/* 弹窗动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

/* 营养素分布 */
.nutrient-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.nutrient-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  box-shadow: var(--shadow);
}

.nutrient-card.carbs {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
}

.nutrient-card.protein {
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
}

.nutrient-card.fat {
  background: linear-gradient(135deg, #fffbeb, #fef3c7);
}

.nutrient-icon {
  font-size: 32rpx;
  margin-bottom: 16rpx;
}

.nutrient-label {
  font-size: 20rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
}

.nutrient-percentage {
  font-size: 32rpx;
  font-weight: bold;
}

.nutrient-card.carbs .nutrient-percentage {
  color: #ef4444;
}

.nutrient-card.protein .nutrient-percentage {
  color: #3b82f6;
}

.nutrient-card.fat .nutrient-percentage {
  color: #f59e0b;
}

/* 快捷操作 */
.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin: 0 32rpx 32rpx;
}

.quick-action-card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  text-align: center;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.quick-action-card:active {
  transform: scale(0.95);
}

.action-icon-container {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
}

.action-icon-container.primary {
  background: var(--primary-color);
}

.action-icon-container.success {
  background: var(--success-color);
}

.action-icon {
  font-size: 48rpx;
  color: #ffffff;
}

.action-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.action-subtitle {
  font-size: 20rpx;
  color: var(--text-secondary);
}

/* 今日运动 */
.sport-overview {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sport-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.sport-icon-container {
  width: 80rpx;
  height: 80rpx;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sport-icon {
  font-size: 32rpx;
  color: var(--success-color);
}

.sport-details {
  flex: 1;
}

.sport-calories {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.sport-activity {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.sport-action {
  margin-left: 24rpx;
}

.add-sport-btn {
  padding: 16rpx 32rpx;
  background: var(--success-color);
  color: white;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.add-sport-btn:active {
  opacity: 0.8;
}

/* 移除底部导航后的容器样式 */
.container {
  padding-bottom: 32rpx;
}

/* 图标样式 */
.profile-avatar-icon {
  font-size: 48rpx;
  color: rgba(255, 255, 255, 0.9);
}

.card-title-icon {
  font-size: 32rpx;
  color: var(--primary-color);
  margin-right: 16rpx;
}

.nutrient-icon {
  font-size: 40rpx;
  margin-bottom: 16rpx;
}

.carbs-icon {
  color: #f59e0b;
}

.protein-icon {
  color: #ef4444;
}

.fat-icon {
  color: #10b981;
}

.action-icon {
  font-size: 48rpx;
  color: #ffffff;
}

.sport-fire-icon {
  font-size: 32rpx;
  color: #ef4444;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .profile-stats {
    gap: 16rpx;
  }

  .stat-value {
    font-size: 24rpx;
  }

  .calorie-consumed {
    font-size: 40rpx;
  }

  .quick-actions {
    gap: 16rpx;
  }

  .action-item {
    padding: 24rpx;
  }

  .action-icon {
    font-size: 40rpx;
  }

  .action-name {
    font-size: 24rpx;
  }
}
