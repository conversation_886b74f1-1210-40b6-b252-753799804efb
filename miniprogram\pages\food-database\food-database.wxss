/* 食物库页面样式 */
.page-container {
  background-color: #f8fafc;
  min-height: 100vh;
}

.container {
  padding: 32rpx;
}

/* 搜索栏 */
.search-container {
  margin-bottom: 32rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 24rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.search-icon {
  font-size: 32rpx;
  color: #94a3b8;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-primary);
}

.clear-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e2e8f0;
  border-radius: 50%;
  font-size: 24rpx;
  color: #64748b;
}

/* 分类标签 */
.category-tabs {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
  overflow-x: auto;
  padding-bottom: 8rpx;
}

.category-tab {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: var(--text-secondary);
  white-space: nowrap;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.category-tab.active {
  background: var(--primary-color);
  color: white;
}

.category-icon {
  font-size: 20rpx;
  margin-right: 8rpx;
}

/* 食物列表 */
.food-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.food-item {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  transition: all 0.3s ease;
}

.food-item:active {
  transform: scale(0.98);
}

.food-info {
  flex: 1;
  margin-right: 24rpx;
  display: flex;
  align-items: flex-start;
}

.food-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  background: #f1f5f9;
}

.food-details {
  flex: 1;
}

.food-name {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.food-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.food-nutrition {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.nutrition-item {
  font-size: 20rpx;
  color: var(--text-tertiary);
  background: #f1f5f9;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.food-calories {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 120rpx;
}

.calories-value {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
}

.calories-unit {
  font-size: 20rpx;
  color: var(--text-secondary);
  margin-top: 4rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.empty-icon {
  font-size: 120rpx;
  color: #e2e8f0;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: var(--text-tertiary);
  text-align: center;
  line-height: 1.5;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f1f5f9;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: var(--text-secondary);
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.nutrition-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.nutrition-card {
  background: #f8fafc;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
}

.nutrition-value {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.nutrition-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.food-description {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}

.weight-examples {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.weight-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background: #f8fafc;
  border-radius: 8rpx;
}

.weight-name {
  font-size: 26rpx;
  color: var(--text-primary);
}

.weight-value {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #f1f5f9;
}

.modal-btn {
  flex: 1;
  padding: 32rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
}

.modal-btn.cancel {
  color: var(--text-secondary);
}

.modal-btn.confirm {
  color: var(--primary-color);
  border-left: 2rpx solid #f1f5f9;
}
