// 饮食日历选择页面
Page({
  data: {
    currentMonth: '',
    currentYear: 2024,
    currentMonthIndex: 0,
    weekDays: ['日', '一', '二', '三', '四', '五', '六'],
    calendarDays: []
  },

  onLoad() {
    this.initCalendar();
  },

  initCalendar() {
    const now = new Date();
    this.setData({
      currentYear: now.getFullYear(),
      currentMonthIndex: now.getMonth()
    });
    this.generateCalendar();
  },

  generateCalendar() {
    const { currentYear, currentMonthIndex } = this.data;
    const firstDay = new Date(currentYear, currentMonthIndex, 1);
    const lastDay = new Date(currentYear, currentMonthIndex + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const calendarDays = [];
    const today = new Date();
    const todayStr = this.formatDate(today);

    // 生成6周的日期
    for (let i = 0; i < 42; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      
      const dateStr = this.formatDate(currentDate);
      const isCurrentMonth = currentDate.getMonth() === currentMonthIndex;
      const isToday = dateStr === todayStr;

      calendarDays.push({
        date: dateStr,
        day: currentDate.getDate(),
        isCurrentMonth,
        isToday,
        isSelected: false
      });
    }

    const monthNames = [
      '1月', '2月', '3月', '4月', '5月', '6月',
      '7月', '8月', '9月', '10月', '11月', '12月'
    ];

    this.setData({
      calendarDays,
      currentMonth: `${currentYear}年${monthNames[currentMonthIndex]}`
    });
  },

  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  formatDateStr(dateStr) {
    const date = new Date(dateStr);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weekDay = weekDays[date.getDay()];
    return `${month}月${day}日 ${weekDay}`;
  },

  onPrevMonth() {
    let { currentYear, currentMonthIndex } = this.data;
    currentMonthIndex--;
    if (currentMonthIndex < 0) {
      currentMonthIndex = 11;
      currentYear--;
    }
    this.setData({ currentYear, currentMonthIndex });
    this.generateCalendar();
  },

  onNextMonth() {
    let { currentYear, currentMonthIndex } = this.data;
    currentMonthIndex++;
    if (currentMonthIndex > 11) {
      currentMonthIndex = 0;
      currentYear++;
    }
    this.setData({ currentYear, currentMonthIndex });
    this.generateCalendar();
  },

  onSelectDay(e) {
    const { date } = e.currentTarget.dataset;
    const selectedDay = this.data.calendarDays.find(day => day.date === date);

    if (selectedDay && selectedDay.isCurrentMonth) {
      // 直接跳转，不需要确认
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];

      if (prevPage && prevPage.route === 'pages/food/food') {
        // 调用饮食页面的方法来切换日期
        const selectedDate = new Date(date);
        prevPage.switchToDate(selectedDate);
      }

      wx.navigateBack();
    }
  }
});
