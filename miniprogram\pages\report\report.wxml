<!-- 智食派数据报告页面 -->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="container">
    <!-- 体重趋势 -->
    <view class="card">
      <view class="card-title">
        <text class="iconfont icon-qushitu card-title-icon"></text>
        体重趋势
      </view>

      <view class="weight-summary">
        <view class="weight-item">
          <view class="weight-value">{{weightTrend.currentWeight}}kg</view>
          <view class="weight-label">当前体重</view>
        </view>
        <view class="weight-item">
          <view class="weight-value" style="color: {{weightTrend.weightChange < 0 ? '#10b981' : '#ef4444'}}">
            {{weightTrend.weightChange > 0 ? '+' : ''}}{{weightTrend.weightChange}}kg
          </view>
          <view class="weight-label">变化</view>
        </view>
        <view class="weight-item">
          <view class="weight-value">{{weightTrend.weeklyRate}}kg/周</view>
          <view class="weight-label">减重速度</view>
        </view>
      </view>

      <!-- 模拟体重趋势图 -->
      <view class="weight-chart">
        <view class="chart-title">近7天体重变化</view>
        <view class="chart-container">
          <view class="chart-y-axis">
            <view class="y-label">72kg</view>
            <view class="y-label">70kg</view>
            <view class="y-label">68kg</view>
            <view class="y-label">66kg</view>
          </view>
          <view class="chart-area">
            <view class="chart-line">
              <view class="chart-point" style="left: 0%; bottom: 60%;" data-value="70.2kg"></view>
              <view class="chart-point" style="left: 16.67%; bottom: 55%;" data-value="69.8kg"></view>
              <view class="chart-point" style="left: 33.33%; bottom: 50%;" data-value="69.5kg"></view>
              <view class="chart-point" style="left: 50%; bottom: 45%;" data-value="69.1kg"></view>
              <view class="chart-point" style="left: 66.67%; bottom: 40%;" data-value="68.8kg"></view>
              <view class="chart-point" style="left: 83.33%; bottom: 35%;" data-value="68.5kg"></view>
              <view class="chart-point active" style="left: 100%; bottom: 30%;" data-value="68.2kg"></view>
            </view>
          </view>
        </view>
        <view class="chart-x-axis">
          <view class="x-label">周一</view>
          <view class="x-label">周二</view>
          <view class="x-label">周三</view>
          <view class="x-label">周四</view>
          <view class="x-label">周五</view>
          <view class="x-label">周六</view>
          <view class="x-label">周日</view>
        </view>
      </view>

      <view class="health-status" style="color: {{weightTrend.healthStatus.color}}">
        {{weightTrend.healthStatus.message}}
      </view>
    </view>

    <!-- 热量平衡 -->
    <view class="card">
      <view class="card-title">
        <text class="iconfont icon-tizhong card-title-icon"></text>
        热量平衡
      </view>

      <view class="calorie-summary">
        <view class="calorie-item">
          <view class="calorie-value">{{calorieBalance.avgIntake}}</view>
          <view class="calorie-label">平均摄入</view>
        </view>
        <view class="calorie-item">
          <view class="calorie-value">{{calorieBalance.avgBurn}}</view>
          <view class="calorie-label">平均消耗</view>
        </view>
        <view class="calorie-item">
          <view class="calorie-value">{{calorieBalance.avgDeficit}}</view>
          <view class="calorie-label">热量缺口</view>
        </view>
      </view>
    </view>

    <!-- 习惯分析 -->
    <view class="card">
      <view class="card-title">
        <text class="iconfont2 icon2-fenxi card-title-icon"></text>
        习惯分析
      </view>

      <view class="habit-stats">
        <view class="habit-item">
          <view class="habit-percentage">{{habitAnalysis.recordFrequency}}%</view>
          <view class="habit-label">记录频率</view>
        </view>
      </view>
    </view>

    <!-- 减重预测 -->
    <view class="card">
      <view class="card-title">
        <text class="iconfont2 icon2-yuce card-title-icon"></text>
        减重预测
      </view>

      <view class="prediction-content">
        <view class="prediction-item">
          <view class="prediction-value">{{prediction.currentProgress}}%</view>
          <view class="prediction-label">目标完成度</view>
        </view>
        <view class="prediction-item">
          <view class="prediction-value">{{prediction.estimatedWeeks}}周</view>
          <view class="prediction-label">预计剩余时间</view>
        </view>
      </view>
    </view>

    <!-- 日历记录 -->
    <view class="card">
      <view class="card-title">
        <text class="iconfont icon-qushitu card-title-icon"></text>
        日历记录
        <view class="view-all-btn" bindtap="onViewCalendar">查看全部</view>
      </view>

      <view class="calendar-preview">
        <view class="preview-desc">记录每日热量摄入和体重变化</view>
        <view class="quick-stats">
          <view class="stat-item">
            <view class="stat-value">15</view>
            <view class="stat-label">本月记录天数</view>
          </view>
          <view class="stat-item">
            <view class="stat-value">1850</view>
            <view class="stat-label">平均热量 (kcal)</view>
          </view>
          <view class="stat-item">
            <view class="stat-value">-1.2</view>
            <view class="stat-label">体重变化 (kg)</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>