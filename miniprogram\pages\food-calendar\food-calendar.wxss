/* 饮食日历选择页面样式 */
.page-container {
  background-color: #f8fafc;
  min-height: 100vh;
}

.container {
  padding: 32rpx;
}

/* 月份导航 */
.month-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.nav-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  border-radius: 50%;
}

.arrow-icon {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

.month-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
}

/* 星期标题 */
.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.week-day {
  text-align: center;
  font-size: 24rpx;
  color: var(--text-secondary);
  padding: 16rpx 0;
  font-weight: 600;
}

/* 日历网格 */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  margin-bottom: 32rpx;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  position: relative;
  transition: all 0.3s ease;
}

.calendar-day:active {
  transform: scale(0.95);
}

.calendar-day.other-month {
  opacity: 0.3;
}

.calendar-day.today {
  background: var(--primary-color);
  color: white;
}

.calendar-day.selected {
  background: var(--success-color);
  color: white;
}

.day-number {
  font-size: 28rpx;
  font-weight: 600;
}


