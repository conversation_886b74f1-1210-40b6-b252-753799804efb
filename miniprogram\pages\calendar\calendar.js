// 日历记录页面
Page({
  data: {
    currentMonth: '',
    currentYear: 2024,
    currentMonthIndex: 0,
    weekDays: ['日', '一', '二', '三', '四', '五', '六'],
    calendarDays: [],
    selectedDate: null,
    showEditModal: false,
    editData: {
      calories: '',
      weight: ''
    },
    // 模拟数据 - 使用当前月份
    recordData: {}
  },

  onLoad() {
    this.initCalendar();
    this.initMockData();
  },

  initMockData() {
    // 为当前月份生成一些模拟数据
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    const recordData = {};

    // 生成最近几天的模拟数据
    for (let i = 1; i <= 20; i++) {
      const date = new Date(currentYear, currentMonth, i);
      const dateStr = this.formatDate(date);

      if (Math.random() > 0.3) { // 70% 概率有记录
        recordData[dateStr] = {
          calories: Math.floor(Math.random() * 800) + 1500, // 1500-2300
          weight: Math.random() > 0.5 ? (Math.random() * 5 + 65).toFixed(1) : null // 65-70kg
        };
      }
    }

    this.setData({ recordData });
  },

  initCalendar() {
    const now = new Date();
    this.setData({
      currentYear: now.getFullYear(),
      currentMonthIndex: now.getMonth()
    });
    this.generateCalendar();
  },

  generateCalendar() {
    const { currentYear, currentMonthIndex } = this.data;
    const firstDay = new Date(currentYear, currentMonthIndex, 1);
    const lastDay = new Date(currentYear, currentMonthIndex + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const calendarDays = [];
    const today = new Date();
    const todayStr = this.formatDate(today);

    // 生成6周的日期
    for (let i = 0; i < 42; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      
      const dateStr = this.formatDate(currentDate);
      const isCurrentMonth = currentDate.getMonth() === currentMonthIndex;
      const isToday = dateStr === todayStr;
      const hasRecord = !!this.data.recordData[dateStr];

      calendarDays.push({
        date: dateStr,
        day: currentDate.getDate(),
        isCurrentMonth,
        isToday,
        hasRecord,
        calories: this.data.recordData[dateStr]?.calories,
        weight: this.data.recordData[dateStr]?.weight
      });
    }

    const monthNames = [
      '1月', '2月', '3月', '4月', '5月', '6月',
      '7月', '8月', '9月', '10月', '11月', '12月'
    ];

    this.setData({
      calendarDays,
      currentMonth: `${currentYear}年${monthNames[currentMonthIndex]}`
    });
  },

  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  formatDateStr(dateStr) {
    const date = new Date(dateStr);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}月${day}日`;
  },

  onPrevMonth() {
    let { currentYear, currentMonthIndex } = this.data;
    currentMonthIndex--;
    if (currentMonthIndex < 0) {
      currentMonthIndex = 11;
      currentYear--;
    }
    this.setData({ currentYear, currentMonthIndex });
    this.generateCalendar();
  },

  onNextMonth() {
    let { currentYear, currentMonthIndex } = this.data;
    currentMonthIndex++;
    if (currentMonthIndex > 11) {
      currentMonthIndex = 0;
      currentYear++;
    }
    this.setData({ currentYear, currentMonthIndex });
    this.generateCalendar();
  },

  onSelectDay(e) {
    const { date } = e.currentTarget.dataset;
    const selectedDay = this.data.calendarDays.find(day => day.date === date);
    
    if (selectedDay && selectedDay.isCurrentMonth) {
      this.setData({
        selectedDate: {
          ...selectedDay,
          dateStr: this.formatDateStr(date)
        }
      });
    }
  },

  onEditRecord() {
    const { selectedDate } = this.data;
    this.setData({
      showEditModal: true,
      editData: {
        calories: selectedDate.calories || '',
        weight: selectedDate.weight || ''
      }
    });
  },

  onCloseModal() {
    this.setData({
      showEditModal: false,
      editData: { calories: '', weight: '' }
    });
  },

  stopPropagation() {
    // 阻止事件冒泡
  },

  onCaloriesInput(e) {
    this.setData({
      'editData.calories': e.detail.value
    });
  },

  onWeightInput(e) {
    this.setData({
      'editData.weight': e.detail.value
    });
  },

  onSaveRecord() {
    const { selectedDate, editData } = this.data;
    const { calories, weight } = editData;

    // 更新记录数据
    const recordData = { ...this.data.recordData };
    recordData[selectedDate.date] = {
      calories: calories ? parseInt(calories) : null,
      weight: weight ? parseFloat(weight) : null
    };

    this.setData({
      recordData,
      showEditModal: false
    });

    // 重新生成日历以更新显示
    this.generateCalendar();

    // 更新选中日期的数据
    this.setData({
      'selectedDate.calories': recordData[selectedDate.date].calories,
      'selectedDate.weight': recordData[selectedDate.date].weight
    });

    wx.showToast({
      title: '保存成功',
      icon: 'success'
    });
  }
});
