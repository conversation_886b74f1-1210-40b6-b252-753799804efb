/* 智食派数据报告页面样式 */

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-text {
  color: var(--text-secondary);
  font-size: 32rpx;
}

/* 体重趋势 */
.weight-summary {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.weight-item {
  text-align: center;
  padding: 24rpx;
  background: var(--bg-primary);
  border-radius: 16rpx;
}

.weight-value {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.weight-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.health-status {
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  padding: 16rpx;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 16rpx;
}

/* 热量平衡 */
.calorie-summary {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24rpx;
}

.calorie-item {
  text-align: center;
  padding: 24rpx;
  background: var(--bg-primary);
  border-radius: 16rpx;
}

.calorie-value {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.calorie-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 习惯分析 */
.habit-stats {
  text-align: center;
}

.habit-item {
  padding: 32rpx;
  background: var(--bg-primary);
  border-radius: 16rpx;
}

.habit-percentage {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--success-color);
  margin-bottom: 8rpx;
}

.habit-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 减重预测 */
.prediction-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.prediction-item {
  text-align: center;
  padding: 24rpx;
  background: var(--bg-primary);
  border-radius: 16rpx;
}

.prediction-value {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--warning-color);
  margin-bottom: 8rpx;
}

.prediction-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 体重趋势图样式 */
.weight-chart {
  margin-top: 32rpx;
}

.chart-title {
  font-size: 28rpx;
  color: var(--text-primary);
  margin-bottom: 24rpx;
  font-weight: 600;
}

.chart-container {
  display: flex;
  height: 200rpx;
  margin-bottom: 16rpx;
}

.chart-y-axis {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 80rpx;
  padding-right: 16rpx;
}

.y-label {
  font-size: 20rpx;
  color: var(--text-secondary);
  text-align: right;
}

.chart-area {
  flex: 1;
  position: relative;
  background: linear-gradient(to bottom,
    transparent 0%,
    rgba(37, 99, 235, 0.05) 25%,
    rgba(37, 99, 235, 0.1) 50%,
    rgba(37, 99, 235, 0.05) 75%,
    transparent 100%);
  border-radius: 8rpx;
}

.chart-line {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-point {
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  background: var(--primary-color);
  border-radius: 50%;
  transform: translate(-50%, 50%);
  box-shadow: 0 0 0 6rpx rgba(37, 99, 235, 0.2);
}

.chart-point.active {
  background: var(--danger-color);
  box-shadow: 0 0 0 6rpx rgba(239, 68, 68, 0.2);
}

.chart-x-axis {
  display: flex;
  justify-content: space-between;
  padding-left: 96rpx;
}

.x-label {
  font-size: 20rpx;
  color: var(--text-secondary);
  text-align: center;
  flex: 1;
}

/* 日历预览样式 */
.view-all-btn {
  background: var(--primary-color);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
}

.calendar-preview {
  margin-top: 24rpx;
}

.preview-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: 24rpx;
}

.quick-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.stat-item {
  background: #f8fafc;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: var(--text-secondary);
}

/* 图标样式 */
.card-title-icon {
  font-size: 32rpx;
  color: var(--primary-color);
  margin-right: 16rpx;
}