// 饮食记录页面
Page({
  data: {
    // 当前日期
    currentDate: {
      main: '今天',
      sub: '8月6日 周三'
    },
    
    // 热量数据
    dailyCalories: {
      consumed: 1245,
      target: 1500,
      burned: 320,
      remaining: 255
    },

    // 水位高度和进度
    waterHeight: 0,
    calorieProgress: 0,

    // 智能提示
    smartTip: {
      show: true,
      icon: 'icon-xinzengtubiao-03',
      title: '热量摄入充足',
      description: '今日已达到目标的83%，建议适量增加蛋白质摄入'
    },

    // 餐次数据
    meals: [
      {
        type: 'breakfast',
        name: '早餐',
        icon: 'icon-zaocan',
        totalCalories: 420,
        foods: [
          {
            id: 1,
            name: '燕麦粥',
            amount: '1碗 (200g)',
            calories: 180,
            image: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=80&h=80&fit=crop&crop=center'
          },
          {
            id: 2,
            name: '香蕉',
            amount: '1根 (120g)',
            calories: 105,
            image: 'https://images.unsplash.com/photo-1569288052389-dac9b01ac5b9?w=80&h=80&fit=crop&crop=center'
          },
          {
            id: 3,
            name: '低脂牛奶',
            amount: '1杯 (250ml)',
            calories: 135,
            image: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=80&h=80&fit=crop&crop=center'
          }
        ]
      },
      {
        type: 'lunch',
        name: '午餐',
        icon: 'icon-wucan',
        totalCalories: 580,
        foods: [
          {
            id: 4,
            name: '鸡胸肉',
            amount: '150g',
            calories: 248,
            image: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?w=80&h=80&fit=crop&crop=center'
          },
          {
            id: 5,
            name: '糙米饭',
            amount: '1碗 (150g)',
            calories: 216,
            image: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=80&h=80&fit=crop&crop=center'
          },
          {
            id: 6,
            name: '蔬菜沙拉',
            amount: '1份 (200g)',
            calories: 116,
            image: 'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=80&h=80&fit=crop&crop=center'
          }
        ]
      },
      {
        type: 'dinner',
        name: '晚餐',
        icon: 'icon-wancan',
        totalCalories: 245,
        foods: [
          {
            id: 7,
            name: '蔬菜汤',
            amount: '1碗 (300ml)',
            calories: 85,
            image: 'https://images.unsplash.com/photo-1547592180-85f173990554?w=80&h=80&fit=crop&crop=center'
          },
          {
            id: 8,
            name: '苹果',
            amount: '1个 (200g)',
            calories: 104,
            image: 'https://images.unsplash.com/photo-1551754655-cd27e38d2076?w=80&h=80&fit=crop&crop=center'
          },
          {
            id: 9,
            name: '希腊酸奶',
            amount: '1杯 (100g)',
            calories: 56,
            image: 'https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=80&h=80&fit=crop&crop=center'
          }
        ]
      },
      {
        type: 'snack',
        name: '加餐',
        icon: 'icon-ewaijiacan',
        totalCalories: 0,
        foods: []
      }
    ],

    // 添加选项弹窗
    showAddOptions: false,
    selectedMeal: '',

    // 当前日期对象
    currentDateObj: new Date()
  },

  onLoad() {
    this.initDate();
    this.updateSmartTip();
    this.updateWaterLevel();
  },

  // 初始化日期
  initDate() {
    const now = new Date();
    this.updateCurrentDate(now);
  },

  // 更新水位高度
  updateWaterLevel() {
    const { consumed, target } = this.data.dailyCalories;
    const progress = Math.min((consumed / target) * 100, 100);

    // 水位高度，最小10%，最大90%
    const waterHeight = Math.max(10, Math.min(90, progress));

    this.setData({
      waterHeight: waterHeight,
      calorieProgress: Math.round(progress)
    });
  },

  // 更新智能提示
  updateSmartTip() {
    const { consumed, target, remaining } = this.data.dailyCalories;
    const progress = Math.round((consumed / target) * 100);
    
    let tip = {};
    
    if (remaining <= 0) {
      tip = {
        show: true,
        icon: 'icon-xinzengtubiao-03',
        title: '已达到目标热量',
        description: '今日热量摄入已达标，建议适量运动消耗多余热量'
      };
    } else if (progress >= 80) {
      tip = {
        show: true,
        icon: 'icon-xinzengtubiao-03',
        title: '热量摄入充足',
        description: `今日已达到目标的${progress}%，建议适量增加蛋白质摄入`
      };
    } else if (progress >= 50) {
      tip = {
        show: true,
        icon: 'icon-xinzengtubiao-03',
        title: '继续保持',
        description: `还需摄入${remaining}kcal，建议选择营养均衡的食物`
      };
    } else {
      tip = {
        show: true,
        icon: 'icon-xinzengtubiao-03',
        title: '热量摄入不足',
        description: `还需摄入${remaining}kcal，建议增加主食和蛋白质`
      };
    }

    this.setData({ smartTip: tip });
  },



  // 打开日历选择器
  onOpenCalendar() {
    wx.navigateTo({
      url: '/pages/food-calendar/food-calendar'
    });
  },

  // 切换到指定日期（由日历页面调用）
  switchToDate(date) {
    this.updateCurrentDate(date);
    this.loadDateData(date);
    this.updateWaterLevel(); // 切换日期后更新水位
  },

  // 上一天
  onPrevDay() {
    const currentDate = new Date(this.data.currentDateObj);
    currentDate.setDate(currentDate.getDate() - 1);
    this.updateCurrentDate(currentDate);
    this.loadDateData(currentDate);
  },

  // 下一天
  onNextDay() {
    const currentDate = new Date(this.data.currentDateObj);
    currentDate.setDate(currentDate.getDate() + 1);
    this.updateCurrentDate(currentDate);
    this.loadDateData(currentDate);
  },

  // 更新当前日期显示
  updateCurrentDate(date) {
    const now = new Date();
    const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const weekDay = weekDays[date.getDay()];

    // 判断是否是今天
    const isToday = date.toDateString() === now.toDateString();
    const isYesterday = date.toDateString() === new Date(now.getTime() - 24 * 60 * 60 * 1000).toDateString();
    const isTomorrow = date.toDateString() === new Date(now.getTime() + 24 * 60 * 60 * 1000).toDateString();

    let mainText = '';
    if (isToday) {
      mainText = '今天';
    } else if (isYesterday) {
      mainText = '昨天';
    } else if (isTomorrow) {
      mainText = '明天';
    } else {
      mainText = `${month}月${day}日`;
    }

    this.setData({
      currentDate: {
        main: mainText,
        sub: `${month}月${day}日 ${weekDay}`
      },
      currentDateObj: date
    });
  },

  // 加载指定日期的数据
  loadDateData(date) {
    // 这里可以根据日期加载对应的饮食数据
    // 暂时显示提示
    const dateStr = date.toLocaleDateString();
    wx.showToast({
      title: `加载${dateStr}的数据`,
      icon: 'none'
    });
  },

  // 添加食物到指定餐次
  onAddFood(e) {
    const mealType = e.currentTarget.dataset.meal;
    this.setData({
      selectedMeal: mealType,
      showAddOptions: true
    });
  },

  // 显示添加选项
  onShowAddOptions() {
    this.setData({
      showAddOptions: true
    });
  },

  // 隐藏添加选项
  onHideAddOptions() {
    this.setData({
      showAddOptions: false,
      selectedMeal: ''
    });
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 拍照识别
  onTakePhoto() {
    this.onHideAddOptions();
    wx.navigateTo({
      url: '/pages/camera/camera'
    });
  },

  // 语音记录
  onVoiceRecord() {
    this.onHideAddOptions();
    wx.showToast({
      title: '语音记录功能开发中',
      icon: 'none'
    });
  },

  // 打开食物库
  onOpenFoodDatabase() {
    this.onHideAddOptions();
    wx.navigateTo({
      url: '/pages/food-database/food-database'
    });
  },

  onPullDownRefresh() {
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 测试方法：模拟热量变化（可以在开发时使用）
  simulateCalorieChange() {
    const newConsumed = Math.floor(Math.random() * 2000) + 500; // 500-2500
    const remaining = Math.max(0, this.data.dailyCalories.target - newConsumed);

    this.setData({
      'dailyCalories.consumed': newConsumed,
      'dailyCalories.remaining': remaining
    });

    this.updateWaterLevel();
    this.updateSmartTip();
  }
});
