<!-- 饮食记录页面 -->
<view class="page-container">
  <view class="container">
    <!-- 顶部导航 -->
    <view class="top-nav">
      <view class="nav-left"></view>
      <view class="nav-title">今日饮食</view>
      <view class="nav-right" bindtap="onOpenCalendar">
        <text class="iconfont icon-qushitu nav-icon"></text>
      </view>
    </view>

    <!-- 日期选择 -->
    <view class="date-selector">
      <view class="date-nav">
        <view class="date-arrow" bindtap="onPrevDay">
          <text class="arrow-icon">‹</text>
        </view>
        <view class="date-info">
          <view class="date-main">{{currentDate.main}}</view>
          <view class="date-sub">{{currentDate.sub}}</view>
        </view>
        <view class="date-arrow" bindtap="onNextDay">
          <text class="arrow-icon">›</text>
        </view>
      </view>
    </view>

    <!-- 热量总览卡片 -->
    <view class="calorie-overview-card" bindlongpress="simulateCalorieChange">
      <!-- 水壶容器 -->
      <view class="water-container">
        <view class="water-bottle">
          <!-- 水流动画 -->
          <view class="water-flow" style="height: {{waterHeight}}%;">
            <view class="water-wave wave1"></view>
            <view class="water-wave wave2"></view>
            <view class="water-wave wave3"></view>
          </view>

          <!-- 刻度线 -->
          <view class="scale-lines">
            <view class="scale-line" style="bottom: 25%;">
              <view class="scale-text">25%</view>
            </view>
            <view class="scale-line" style="bottom: 50%;">
              <view class="scale-text">50%</view>
            </view>
            <view class="scale-line" style="bottom: 75%;">
              <view class="scale-text">75%</view>
            </view>
          </view>
        </view>

        <!-- 热量数据 -->
        <view class="calorie-data">
          <view class="calorie-main">
            <view class="calorie-value">{{dailyCalories.consumed}}</view>
            <view class="calorie-label">已摄入热量 (kcal)</view>
            <view class="calorie-progress">{{calorieProgress}}%</view>
          </view>
        </view>
      </view>

      <!-- 统计数据 -->
      <view class="calorie-stats">
        <view class="stat-item">
          <view class="stat-value">{{dailyCalories.remaining}}</view>
          <view class="stat-label">剩余</view>
        </view>
        <view class="stat-item">
          <view class="stat-value">{{dailyCalories.target}}</view>
          <view class="stat-label">目标</view>
        </view>
        <view class="stat-item">
          <view class="stat-value">{{dailyCalories.burned}}</view>
          <view class="stat-label">运动消耗</view>
        </view>
      </view>
    </view>

    <!-- 智能提示卡片 -->
    <view class="smart-tip-card" wx:if="{{smartTip.show}}">
      <view class="tip-icon">
        <text class="iconfont {{smartTip.icon}} tip-icon-text"></text>
      </view>
      <view class="tip-content">
        <view class="tip-title">{{smartTip.title}}</view>
        <view class="tip-desc">{{smartTip.description}}</view>
      </view>
    </view>

    <!-- 餐次记录 -->
    <view class="meals-container">
      <view 
        class="meal-card"
        wx:for="{{meals}}" 
        wx:key="type"
        wx:for-item="meal"
      >
        <view class="meal-header">
          <view class="meal-info">
            <view class="meal-icon-container {{meal.type}}">
              <text class="iconfont {{meal.icon}} meal-icon"></text>
            </view>
            <view class="meal-details">
              <view class="meal-name">{{meal.name}}</view>
              <view class="meal-calories">{{meal.totalCalories}} kcal</view>
            </view>
          </view>
          <view class="meal-add-btn" bindtap="onAddFood" data-meal="{{meal.type}}">
            添加
          </view>
        </view>

        <!-- 食物列表 -->
        <view class="food-list" wx:if="{{meal.foods.length > 0}}">
          <view 
            class="food-item"
            wx:for="{{meal.foods}}" 
            wx:key="id"
            wx:for-item="food"
          >
            <view class="food-info">
              <image class="food-image" src="{{food.image}}" mode="aspectFill"></image>
              <view class="food-details">
                <view class="food-name">{{food.name}}</view>
                <view class="food-amount">{{food.amount}}</view>
              </view>
            </view>
            <view class="food-calories">{{food.calories}} kcal</view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="meal-empty" wx:if="{{meal.foods.length === 0}}">
          <text class="empty-text">暂无记录</text>
        </view>
      </view>
    </view>

    <!-- 快捷添加按钮 -->
    <view class="fab-container">
      <view class="fab-button" bindtap="onShowAddOptions">
        <text class="iconfont icon-xinzengtubiao-03 fab-icon"></text>
      </view>
    </view>

    <!-- 添加选项弹窗 -->
    <view wx:if="{{showAddOptions}}" class="add-options-overlay" bindtap="onHideAddOptions">
      <view class="add-options-container" catchtap="stopPropagation">
        <view class="add-option" bindtap="onTakePhoto">
          <text class="iconfont icon-paizhao option-icon"></text>
          <text class="option-text">拍照识别</text>
        </view>
        <view class="add-option" bindtap="onVoiceRecord">
          <text class="iconfont icon-yuyin option-icon"></text>
          <text class="option-text">语音记录</text>
        </view>
        <view class="add-option" bindtap="onOpenFoodDatabase">
          <text class="iconfont icon-gerendangan option-icon"></text>
          <text class="option-text">食物库</text>
        </view>
      </view>
    </view>
  </view>
</view>
